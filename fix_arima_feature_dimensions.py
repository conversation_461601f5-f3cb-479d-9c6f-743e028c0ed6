#!/usr/bin/env python
"""
Fix ARIMA Feature Dimension Mismatches

This script addresses the recurring feature dimension issues in ARIMA ensemble models.
"""

import logging
import numpy as np
from pathlib import Path

logger = logging.getLogger(__name__)

def analyze_feature_dimension_issues():
    """Analyze the feature dimension mismatch patterns."""
    
    issues = {
        'array_slicing_errors': [
            "Array slicing error during exog scaling: InvalidIndexError",
            "Created fallback zero array with shape (100, 20)"
        ],
        'pca_mismatches': [
            "PCA feature mismatch: got 21, expected 20",
            "PCA feature mismatch: got 300, expected 20"
        ],
        'meta_model_mismatches': [
            "Feature dimension mismatch: X has 7 features, but meta-model expects 17",
            "Padded features from 7 to 17 using statistical features"
        ]
    }
    
    logger.info("📊 Feature dimension issues identified:")
    for category, problems in issues.items():
        logger.info(f"  {category}: {len(problems)} issues")
        for problem in problems:
            logger.info(f"    - {problem}")
    
    return issues

def create_feature_standardization_fix():
    """Create a feature standardization fix."""
    
    fix_code = '''
def standardize_exogenous_features(X, target_shape=(None, 20)):
    """
    Standardize exogenous features to consistent dimensions.
    
    Args:
        X: Input features (various shapes)
        target_shape: Target shape (samples, features)
    
    Returns:
        Standardized features with consistent dimensions
    """
    try:
        # Handle different input shapes
        if X.ndim == 3:  # (samples, sequence, features)
            # Flatten sequence dimension
            X_flat = X.reshape(X.shape[0], -1)
        elif X.ndim == 2:  # (samples, features)
            X_flat = X
        else:
            raise ValueError(f"Unsupported input shape: {X.shape}")
        
        # Standardize to target feature count
        target_features = target_shape[1] if target_shape[1] is not None else 20
        
        if X_flat.shape[1] > target_features:
            # Truncate excess features
            X_std = X_flat[:, :target_features]
        elif X_flat.shape[1] < target_features:
            # Pad with statistical features
            padding_size = target_features - X_flat.shape[1]
            
            # Create statistical padding
            mean_vals = np.mean(X_flat, axis=1, keepdims=True)
            std_vals = np.std(X_flat, axis=1, keepdims=True)
            min_vals = np.min(X_flat, axis=1, keepdims=True)
            max_vals = np.max(X_flat, axis=1, keepdims=True)
            
            # Repeat statistical features to fill padding
            stats = np.hstack([mean_vals, std_vals, min_vals, max_vals])
            padding_repeats = (padding_size + stats.shape[1] - 1) // stats.shape[1]
            padding = np.tile(stats, (1, padding_repeats))[:, :padding_size]
            
            X_std = np.hstack([X_flat, padding])
        else:
            # Already correct size
            X_std = X_flat
        
        return X_std
        
    except Exception as e:
        logger.warning(f"Feature standardization failed: {e}")
        # Fallback: create zero array with correct shape
        return np.zeros((X.shape[0] if X.ndim > 0 else 1, target_features))

def fix_pca_dimension_mismatch(pca_transformer, X):
    """
    Fix PCA dimension mismatches.
    
    Args:
        pca_transformer: Fitted PCA transformer
        X: Input features
    
    Returns:
        PCA-transformed features with correct dimensions
    """
    try:
        expected_features = pca_transformer.n_features_in_
        
        if X.shape[1] != expected_features:
            logger.warning(f"PCA feature mismatch: got {X.shape[1]}, expected {expected_features}")
            
            if X.shape[1] > expected_features:
                # Truncate to expected size
                X_fixed = X[:, :expected_features]
            else:
                # Pad to expected size
                padding = np.zeros((X.shape[0], expected_features - X.shape[1]))
                X_fixed = np.hstack([X, padding])
        else:
            X_fixed = X
        
        return pca_transformer.transform(X_fixed)
        
    except Exception as e:
        logger.error(f"PCA transform failed: {e}")
        # Return original features if PCA fails
        return X

def create_ensemble_feature_processor():
    """Create a robust ensemble feature processor."""
    
    processor_code = '''
class EnsembleFeatureProcessor:
    """Robust feature processor for ensemble models."""
    
    def __init__(self, target_features=20, pca_components=10):
        self.target_features = target_features
        self.pca_components = pca_components
        self.pca_transformer = None
        self.feature_scaler = None
    
    def fit(self, X):
        """Fit the feature processor."""
        # Standardize features
        X_std = self.standardize_features(X)
        
        # Fit PCA
        from sklearn.decomposition import PCA
        from sklearn.preprocessing import StandardScaler
        
        self.feature_scaler = StandardScaler()
        X_scaled = self.feature_scaler.fit_transform(X_std)
        
        self.pca_transformer = PCA(n_components=min(self.pca_components, X_scaled.shape[1]))
        self.pca_transformer.fit(X_scaled)
        
        return self
    
    def transform(self, X):
        """Transform features consistently."""
        # Standardize features
        X_std = self.standardize_features(X)
        
        # Scale features
        if self.feature_scaler is not None:
            X_scaled = self.feature_scaler.transform(X_std)
        else:
            X_scaled = X_std
        
        # Apply PCA
        if self.pca_transformer is not None:
            X_pca = self.safe_pca_transform(X_scaled)
        else:
            X_pca = X_scaled
        
        return X_pca
    
    def standardize_features(self, X):
        """Standardize input features to consistent dimensions."""
        return standardize_exogenous_features(X, (None, self.target_features))
    
    def safe_pca_transform(self, X):
        """Safely apply PCA transform."""
        return fix_pca_dimension_mismatch(self.pca_transformer, X)
'''
    
    # Save the processor code
    with open("ensemble_feature_processor.py", 'w') as f:
        f.write(processor_code)
    
    logger.info("✅ Created ensemble feature processor")

def main():
    """Main function to fix feature dimension issues."""
    
    logger.info("🔧 Starting ARIMA feature dimension fixes...")
    
    # Analyze current issues
    issues = analyze_feature_dimension_issues()
    
    # Create fixes
    create_feature_standardization_fix()
    create_ensemble_feature_processor()
    
    logger.info("✅ ARIMA feature dimension fixes created")
    logger.info("📝 Next steps:")
    logger.info("  1. Integrate standardize_exogenous_features() into ensemble models")
    logger.info("  2. Replace existing feature processing with EnsembleFeatureProcessor")
    logger.info("  3. Update ARIMA ensemble models to use consistent feature dimensions")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()
