@echo off
echo ============================================================================
echo                    NEURAL NETWORK MODELS TRAINING
echo ============================================================================
echo Training neural network models (LSTM, TFT) on all timeframes
echo.

REM Train LSTM models (High Performance)
echo ============================================================================
echo STEP 1: Training LSTM models (R² = 0.999+)...
echo ============================================================================
python train_lstm_btcusd.py
set LSTM_ERROR=%ERRORLEVEL%

if %LSTM_ERROR% EQU 0 (
    echo [SUCCESS] LSTM models trained successfully
) else (
    echo [FAILED] LSTM training failed
)

echo.

REM Train TFT models (Experimental)
echo ============================================================================
echo STEP 2: Training TFT models (R² = 0.529+ - Experimental)...
echo ============================================================================
call train_all_tft_models.bat
set TFT_ERROR=%ERRORLEVEL%

if %TFT_ERROR% EQU 0 (
    echo [SUCCESS] TFT models trained successfully
) else (
    echo [FAILED] TFT training failed
)

echo.
echo ============================================================================
echo Neural network model training completed!
echo ============================================================================
pause
