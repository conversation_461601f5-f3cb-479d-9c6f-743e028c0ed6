@echo off
setlocal enabledelayedexpansion
echo ============================================================================
echo                    TESTING FIXED ENSEMBLE TRAINING SCRIPT
echo ============================================================================
echo.

REM Test the model checking logic
echo [TEST] Checking ARIMA models...
set ARIMA_COUNT=0
if exist "models\arima_BTCUSD.a_M5" set /A ARIMA_COUNT+=1
if exist "models\arima_BTCUSD.a_M15" set /A ARIMA_COUNT+=1
if exist "models\arima_BTCUSD.a_M30" set /A ARIMA_COUNT+=1
if exist "models\arima_BTCUSD.a_H1" set /A ARIMA_COUNT+=1
if exist "models\arima_BTCUSD.a_H4" set /A ARIMA_COUNT+=1

echo [FOUND] !ARIMA_COUNT!/5 ARIMA models exist

echo.
echo [TEST] Checking LSTM models...
set LSTM_COUNT=0
if exist "models\lstm_BTCUSD.a_M5" set /A LSTM_COUNT+=1
if exist "models\lstm_BTCUSD.a_M15" set /A LSTM_COUNT+=1
if exist "models\lstm_BTCUSD.a_M30" set /A LSTM_COUNT+=1
if exist "models\lstm_BTCUSD.a_H1" set /A LSTM_COUNT+=1
if exist "models\lstm_BTCUSD.a_H4" set /A LSTM_COUNT+=1

echo [FOUND] !LSTM_COUNT!/5 LSTM models exist

echo.
echo [TEST] Variable expansion test...
set SUCCESS_STEPS=0
set /A SUCCESS_STEPS+=1
set /A SUCCESS_STEPS+=1
echo SUCCESS_STEPS = !SUCCESS_STEPS!

if !SUCCESS_STEPS! GEQ 2 (
    echo [SUCCESS] Variable expansion working correctly
) else (
    echo [FAILED] Variable expansion not working
)

echo.
echo ============================================================================
echo                    FIXED SCRIPT TEST COMPLETED
echo ============================================================================
echo.
echo The fixed train_all_arima_lstm_ensemble.bat should now work properly!
echo.
pause
