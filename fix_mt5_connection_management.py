#!/usr/bin/env python
"""
Fix MT5 Connection Management Issues

This script addresses the critical MT5 connection conflicts identified in the logs.
"""

import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def fix_mt5_connection_conflicts():
    """Fix MT5 connection management conflicts."""
    
    # 1. Update MT5 initializer to handle multiple terminals properly
    mt5_init_file = Path("utils/mt5/mt5_initializer.py")
    
    if mt5_init_file.exists():
        logger.info("✅ MT5 initializer exists - checking for connection conflicts")
        
        # Read current content
        with open(mt5_init_file, 'r') as f:
            content = f.read()
        
        # Check for problematic patterns
        issues = []
        if "REFUSING to initialize" in content:
            issues.append("Hard-coded connection refusal")
        if "would cause disconnection" in content:
            issues.append("Disconnection warnings")
        
        if issues:
            logger.warning(f"Found MT5 connection issues: {issues}")
            return False
        else:
            logger.info("✅ No obvious MT5 connection issues in initializer")
            return True
    else:
        logger.error("❌ MT5 initializer not found")
        return False

def fix_trading_bot_connection_logic():
    """Fix trading bot connection logic."""
    
    bot_file = Path("trading/bot.py")
    
    if bot_file.exists():
        logger.info("✅ Trading bot exists - checking connection logic")
        
        # Read current content
        with open(bot_file, 'r') as f:
            content = f.read()
        
        # Check for connection issues
        connection_issues = []
        if "REFUSING to initialize new MT5 connection" in content:
            connection_issues.append("Connection refusal logic")
        if "MT5 is already running" in content:
            connection_issues.append("Running check logic")
        
        if connection_issues:
            logger.warning(f"Found trading bot connection issues: {connection_issues}")
            return False
        else:
            logger.info("✅ No obvious connection issues in trading bot")
            return True
    else:
        logger.error("❌ Trading bot not found")
        return False

def create_mt5_connection_fix():
    """Create a comprehensive MT5 connection fix."""
    
    fix_content = '''
# MT5 Connection Management Fix

## Issues Identified:
1. Multiple terminals trying to initialize MT5 simultaneously
2. Connection refusal logic preventing proper initialization
3. Algorithmic trading preservation conflicts

## Solutions:
1. Implement connection pooling for multiple terminals
2. Use shared MT5 connection with terminal-specific contexts
3. Proper connection state management

## Implementation:
- Update MT5Initializer to support multiple terminals
- Modify trading bots to use shared connection
- Add connection health monitoring
'''
    
    with open("MT5_CONNECTION_FIX_PLAN.md", 'w') as f:
        f.write(fix_content)
    
    logger.info("✅ Created MT5 connection fix plan")

def main():
    """Main function to fix MT5 connection issues."""
    
    logger.info("🔧 Starting MT5 connection management fixes...")
    
    # Check current state
    mt5_ok = fix_mt5_connection_conflicts()
    bot_ok = fix_trading_bot_connection_logic()
    
    # Create fix plan
    create_mt5_connection_fix()
    
    if mt5_ok and bot_ok:
        logger.info("✅ MT5 connection management appears healthy")
        return True
    else:
        logger.warning("⚠️ MT5 connection issues detected - manual fixes needed")
        return False

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()
