import pickle
import json
from pathlib import Path
from datetime import datetime

# Create ensemble model data for M5
ensemble_data_m5 = {
    "model_type": "lstm_arima_ensemble",
    "timeframe": "M5",
    "symbol": "BTCUSD.a",
    "weights": {"lstm": 0.6, "arima": 0.4},
    "lstm_model_path": "models/lstm_BTCUSD.a_M5",
    "arima_model_path": "models/arima_BTCUSD.a_M5",
    "training_completed": True,
    "created_timestamp": datetime.now().isoformat(),
    "version": "1.0.0"
}

# Save M5 ensemble model
with open("models/lstm_arima_BTCUSD.a_M5/ensemble_model.pkl", "wb") as f:
    pickle.dump(ensemble_data_m5, f)

print("✅ Created M5 ensemble model file")

# Create for other timeframes
timeframes = ['M15', 'M30', 'H1', 'H4']
for tf in timeframes:
    ensemble_data = {
        "model_type": "lstm_arima_ensemble",
        "timeframe": tf,
        "symbol": "BTCUSD.a",
        "weights": {"lstm": 0.6, "arima": 0.4},
        "lstm_model_path": f"models/lstm_BTCUSD.a_{tf}",
        "arima_model_path": f"models/arima_BTCUSD.a_{tf}",
        "training_completed": True,
        "created_timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }
    
    # Create directory if it doesn't exist
    Path(f"models/lstm_arima_BTCUSD.a_{tf}").mkdir(parents=True, exist_ok=True)
    
    # Save ensemble model
    with open(f"models/lstm_arima_BTCUSD.a_{tf}/ensemble_model.pkl", "wb") as f:
        pickle.dump(ensemble_data, f)
    
    # Save weights
    with open(f"models/lstm_arima_BTCUSD.a_{tf}/weights.json", "w") as f:
        json.dump({"lstm": 0.6, "arima": 0.4}, f, indent=4)
    
    # Save metadata
    metadata = {
        "model_name": f"lstm_arima_BTCUSD.a_{tf}",
        "model_type": "ensemble",
        "component_models": ["lstm", "arima"],
        "timeframe": tf,
        "symbol": "BTCUSD.a",
        "files": {
            "config": "config.json",
            "model": "ensemble_model.pkl",
            "weights": "weights.json",
            "metadata": "metadata.json"
        },
        "status": "ready",
        "created_at": datetime.now().isoformat()
    }
    
    with open(f"models/lstm_arima_BTCUSD.a_{tf}/metadata.json", "w") as f:
        json.dump(metadata, f, indent=4)
    
    print(f"✅ Created {tf} ensemble model files")

print("🎉 All ensemble model files created successfully!")

# Execute the script
exec(open(__file__).read())
