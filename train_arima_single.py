#!/usr/bin/env python
"""
ARIMA Model Training Script for a Single Timeframe

This script trains an ARIMA model on BTCUSD.a data for a single timeframe.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from pathlib import Path
import json
from datetime import datetime
import argparse
import pickle
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from pmdarima import auto_arima
from typing import Dict, Optional, Tuple, Any
from statsmodels.tsa.stattools import adfuller, acf
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from scipy import stats
import warnings

# Import the ensemble ARIMA model
try:
    from models.ensemble_arima_model import EnsembleARIMAModel
    ENSEMBLE_AVAILABLE = True
except ImportError:
    ENSEMBLE_AVAILABLE = False
    print("Warning: EnsembleARIMAModel not available. Using standard ARIMA models only.")

# Import unified configuration
from config import get_historical_data_path, get_model_path, get_metrics_path, get_plots_path

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

# Suppress warnings
warnings.filterwarnings("ignore")

def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments.

    Returns:
        Namespace containing the parsed arguments
    """
    parser = argparse.ArgumentParser(description='Train ARIMA model for a single timeframe')

    # Required arguments
    parser.add_argument('--timeframe', type=str, required=True,
                        help='Timeframe to train (e.g., M5, H1)')

    # Optional arguments
    parser.add_argument('--target-column', type=str, default='close',
                        help='Target column to predict')
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Proportion of data to use for testing')
    parser.add_argument('--random-state', type=int, default=42,
                        help='Random state for reproducibility')
    parser.add_argument('--data-dir', type=str, default=None,
                        help='Directory containing data files (uses config if not specified)')
    parser.add_argument('--auto-arima', action='store_true', default=True,
                        help='Use auto_arima to find the best parameters')
    parser.add_argument('--p', type=int, default=1,
                        help='AR order')
    parser.add_argument('--d', type=int, default=1,
                        help='Differencing')
    parser.add_argument('--q', type=int, default=1,
                        help='MA order')
    parser.add_argument('--seasonal-p', type=int, default=0,
                        help='Seasonal AR order')
    parser.add_argument('--seasonal-d', type=int, default=0,
                        help='Seasonal differencing')
    parser.add_argument('--seasonal-q', type=int, default=0,
                        help='Seasonal MA order')
    parser.add_argument('--seasonal-m', type=int, default=0,
                        help='Seasonal period')
    parser.add_argument('--use-seasonal', action='store_true', default=False,
                        help='Use seasonal component')

    # Data selection arguments
    parser.add_argument('--max-rows', type=int, default=10000,
                        help='Maximum number of rows to use for training (0 for all data)')
    parser.add_argument('--data-selection', type=str, default='recent',
                        choices=['recent', 'all', 'sample'],
                        help='Data selection method: recent=most recent data, all=all data, sample=sampled data')
    parser.add_argument('--sample-interval', type=int, default=5,
                        help='Interval for sampling data (only used with --data-selection=sample)')

    # Advanced modeling options
    parser.add_argument('--use-ensemble', action='store_true', default=False,
                        help='Use ensemble ARIMA model for improved performance')
    parser.add_argument('--ensemble-models', type=int, default=5,
                        help='Number of models in the ensemble')

    return parser.parse_args()

def test_stationarity(series: pd.Series) -> Tuple[bool, float, Dict[str, float]]:
    """
    Test for stationarity using the Augmented Dickey-Fuller test.

    Args:
        series: Time series to test

    Returns:
        Tuple containing:
        - Boolean indicating if the series is stationary
        - p-value from the test
        - Dictionary with critical values
    """
    # Perform Augmented Dickey-Fuller test
    result = adfuller(series.dropna())

    # Extract results
    adf_statistic = result[0]
    p_value = result[1]
    critical_values = result[4]

    # Determine if stationary (p-value < 0.05)
    is_stationary = p_value < 0.05

    # Log results
    logger.info(f"ADF Statistic: {adf_statistic:.4f}")
    logger.info(f"p-value: {p_value:.4f}")
    logger.info(f"Critical Values: {critical_values}")
    logger.info(f"Series is {'stationary' if is_stationary else 'non-stationary'}")

    return is_stationary, p_value, critical_values

def find_optimal_differencing(series: pd.Series, max_diff: int = 2) -> int:
    """
    Find the optimal differencing order to make a series stationary.

    Args:
        series: Time series to difference
        max_diff: Maximum differencing order to try

    Returns:
        int: Optimal differencing order
    """
    # Check if already stationary
    is_stationary, _, _ = test_stationarity(series)
    if is_stationary:
        logger.info("Series is already stationary, no differencing needed")
        return 0

    # Try differencing until stationary
    for d in range(1, max_diff + 1):
        diff_series = series.diff(d).dropna()
        is_stationary, _, _ = test_stationarity(diff_series)

        if is_stationary:
            logger.info(f"Series becomes stationary after {d} differencing")
            return d

    # If still not stationary, return the maximum differencing order
    logger.warning(f"Series still not stationary after {max_diff} differencing")
    return max_diff

def detect_seasonality(series: pd.Series, max_lag: int = 50) -> Tuple[bool, int]:
    """
    Detect seasonality in a time series using autocorrelation.

    Args:
        series: Time series to analyze
        max_lag: Maximum lag to consider

    Returns:
        Tuple containing:
        - Boolean indicating if seasonality is detected
        - Integer representing the seasonal period (0 if none detected)
    """
    # Calculate autocorrelation
    autocorr = acf(series.dropna(), nlags=max_lag)

    # Find peaks in autocorrelation (excluding lag 0)
    peaks = []
    for i in range(1, len(autocorr) - 1):
        if autocorr[i] > autocorr[i-1] and autocorr[i] > autocorr[i+1] and autocorr[i] > 0.2:
            peaks.append((i, autocorr[i]))

    # Sort peaks by correlation value
    peaks.sort(key=lambda x: x[1], reverse=True)

    if peaks:
        # Get the lag with highest autocorrelation
        seasonal_period = peaks[0][0]
        logger.info(f"Detected potential seasonality with period {seasonal_period}")
        return True, seasonal_period
    else:
        logger.info("No significant seasonality detected")
        return False, 0

def add_features(df: pd.DataFrame, target_column: str, drop_na: bool = True) -> pd.DataFrame:
    """
    Add engineered features to the DataFrame.

    Args:
        df: DataFrame with time series data
        target_column: Name of the target column
        drop_na: Whether to drop NaN values (default: True)

    Returns:
        DataFrame with additional features
    """
    # Create a copy to avoid modifying the original
    result_df = df.copy()

    # Extract the target series
    series = result_df[target_column]

    # Add rolling statistics
    for window in [5, 10, 20, 50, 100]:
        # Rolling mean
        result_df[f'rolling_mean_{window}'] = series.rolling(window=window).mean()

        # Rolling standard deviation
        result_df[f'rolling_std_{window}'] = series.rolling(window=window).std()

        # Rolling min/max
        result_df[f'rolling_min_{window}'] = series.rolling(window=window).min()
        result_df[f'rolling_max_{window}'] = series.rolling(window=window).max()

        # Rolling quantiles
        result_df[f'rolling_q25_{window}'] = series.rolling(window=window).quantile(0.25)
        result_df[f'rolling_q75_{window}'] = series.rolling(window=window).quantile(0.75)

    # Add lag features
    for lag in [1, 2, 3, 5, 10, 20]:
        result_df[f'lag_{lag}'] = series.shift(lag)

    # Add difference features
    for diff_period in [1, 2, 5, 10]:
        result_df[f'diff_{diff_period}'] = series.diff(diff_period)

    # Add percentage change features
    for pct_period in [1, 2, 5, 10]:
        result_df[f'pct_change_{pct_period}'] = series.pct_change(pct_period)

    # Add time-based features
    result_df['hour'] = result_df.index.hour
    result_df['day_of_week'] = result_df.index.dayofweek
    result_df['day_of_month'] = result_df.index.day
    result_df['month'] = result_df.index.month
    result_df['year'] = result_df.index.year

    # Add cyclical time features (sin/cos transformation)
    result_df['hour_sin'] = np.sin(2 * np.pi * result_df.index.hour / 24)
    result_df['hour_cos'] = np.cos(2 * np.pi * result_df.index.hour / 24)
    result_df['day_of_week_sin'] = np.sin(2 * np.pi * result_df.index.dayofweek / 7)
    result_df['day_of_week_cos'] = np.cos(2 * np.pi * result_df.index.dayofweek / 7)
    result_df['month_sin'] = np.sin(2 * np.pi * result_df.index.month / 12)
    result_df['month_cos'] = np.cos(2 * np.pi * result_df.index.month / 12)

    # Add volatility features
    result_df['volatility_5'] = result_df[f'diff_1'].rolling(window=5).std()
    result_df['volatility_10'] = result_df[f'diff_1'].rolling(window=10).std()
    result_df['volatility_20'] = result_df[f'diff_1'].rolling(window=20).std()

    # Add momentum features
    result_df['momentum_5'] = series - series.shift(5)
    result_df['momentum_10'] = series - series.shift(10)
    result_df['momentum_20'] = series - series.shift(20)

    # Drop NaN values created by rolling windows and shifts if requested
    if drop_na:
        result_df = result_df.dropna()
    else:
        # Fill NaN values with appropriate methods
        # For lag and rolling features, forward fill is not appropriate
        # Instead, we'll use the mean of the column
        for col in result_df.columns:
            if col != target_column and col not in df.columns:
                if result_df[col].isna().any():
                    # For the first few rows that can't be forward filled
                    # use the first valid value in the column
                    first_valid = result_df[col].first_valid_index()
                    if first_valid is not None:
                        fill_value = result_df.loc[first_valid, col]
                        result_df[col].fillna(fill_value, inplace=True)

    return result_df

def preprocess_data(series: pd.Series, df: Optional[pd.DataFrame] = None,
                   target_column: Optional[str] = None) -> Tuple[pd.Series, Dict[str, Any]]:
    """
    Preprocess time series data for ARIMA modeling.

    Args:
        series: Time series to preprocess
        df: Optional DataFrame containing the full dataset
        target_column: Name of the target column

    Returns:
        Tuple containing:
        - Preprocessed series
        - Dictionary with preprocessing parameters
    """
    # Store preprocessing parameters
    params = {}

    # Check for stationarity
    is_stationary, _, _ = test_stationarity(series)

    # Find optimal differencing if not stationary
    if not is_stationary:
        d = find_optimal_differencing(series)
        params['d'] = d
    else:
        params['d'] = 0

    # Detect seasonality
    has_seasonality, seasonal_period = detect_seasonality(series)
    params['has_seasonality'] = has_seasonality
    params['seasonal_period'] = seasonal_period

    # Add feature engineering if DataFrame is provided
    if df is not None and target_column is not None:
        try:
            # Add engineered features without dropping NaN values
            logger.info("Adding engineered features to the dataset...")
            feature_df = add_features(df, target_column, drop_na=False)

            # Store feature names for later use
            feature_columns = [col for col in feature_df.columns if col != target_column]
            params['feature_columns'] = feature_columns
            params['has_exog'] = True
            params['feature_df'] = feature_df  # Store the full feature DataFrame

            # Log feature information
            logger.info(f"Added {len(feature_columns)} engineered features")

            # Return the processed series and parameters
            return series, params
        except Exception as e:
            logger.warning(f"Error adding features: {str(e)}")
            logger.warning("Proceeding without feature engineering")
            params['has_exog'] = False
    else:
        params['has_exog'] = False

    # Return the original series and parameters
    # (actual differencing will be handled by the ARIMA model)
    return series, params

def load_data(timeframe: str, data_dir: str, max_rows: int = 10000,
            data_selection: str = 'recent', sample_interval: int = 5) -> Optional[pd.DataFrame]:
    """
    Load data for a specific timeframe.

    Args:
        timeframe: Timeframe to load (e.g., M5, H1)
        data_dir: Directory containing data files
        max_rows: Maximum number of rows to use (0 for all data)
        data_selection: Method for selecting data ('recent', 'all', or 'sample')
        sample_interval: Interval for sampling data

    Returns:
        DataFrame containing the data, or None if loading fails
    """
    try:
        file_path = Path(data_dir) / f"BTCUSD.a_{timeframe}.parquet"
        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return None

        df = pd.read_parquet(file_path)
        total_rows = len(df)

        # Ensure the DataFrame has a datetime index
        if 'time' in df.columns:
            df['time'] = pd.to_datetime(df['time'])
            df.set_index('time', inplace=True)
            logger.info(f"Set DataFrame index to 'time' column")

        # Apply data selection strategy
        if data_selection == 'all' or max_rows == 0 or total_rows <= max_rows:
            # Use all data
            logger.info(f"Using all {total_rows} rows for {timeframe} from {file_path}")
        elif data_selection == 'recent':
            # Use only the most recent rows
            logger.info(f"Using last {max_rows} rows out of {total_rows} for {timeframe} from {file_path}")
            df = df.iloc[-max_rows:]
        elif data_selection == 'sample':
            # Sample data at regular intervals
            if sample_interval <= 1:
                sample_interval = 1

            # Calculate how many rows to skip to get approximately max_rows
            if total_rows > max_rows * sample_interval:
                # If we have more data than needed even after sampling
                skip = total_rows - (max_rows * sample_interval)
                df = df.iloc[skip::sample_interval]
            else:
                # Just sample without skipping
                df = df.iloc[::sample_interval]

            logger.info(f"Sampled {len(df)} rows (interval={sample_interval}) out of {total_rows} for {timeframe}")

        # Check for missing values
        missing_values = df.isnull().sum().sum()
        if missing_values > 0:
            logger.warning(f"Found {missing_values} missing values in the data. Filling with forward fill method.")
            df = df.fillna(method='ffill')
            # If there are still missing values at the beginning, fill with backward fill
            df = df.fillna(method='bfill')

        # Check for duplicate indices
        if df.index.duplicated().any():
            logger.warning(f"Found duplicate timestamps in the data. Keeping the last occurrence.")
            df = df[~df.index.duplicated(keep='last')]

        # Sort by index to ensure time order
        df = df.sort_index()

        # Log data statistics
        logger.info(f"Data statistics for {timeframe}:")
        logger.info(f"  - Time range: {df.index.min()} to {df.index.max()}")
        logger.info(f"  - Number of rows: {len(df)}")

        return df
    except Exception as e:
        logger.error(f"Error loading data for {timeframe}: {str(e)}")
        return None

def main() -> int:
    """
    Main function to train an ARIMA model.

    This function parses command-line arguments, loads data, trains an ARIMA model,
    evaluates it, and saves the model and metrics.

    Returns:
        int: Exit code (0 for success, non-zero for failure)
    """
    try:
        args = parse_args()

        # Determine data directory
        if args.data_dir is None:
            data_dir = get_historical_data_path()
            logger.info(f"Using configured data directory: {data_dir}")
        else:
            data_dir = args.data_dir
            logger.info(f"Using specified data directory: {data_dir}")

        # Load data with the specified parameters
        df = load_data(
            args.timeframe,
            str(data_dir),
            max_rows=args.max_rows,
            data_selection=args.data_selection,
            sample_interval=args.sample_interval
        )
        if df is None:
            logger.error(f"Failed to load data for {args.timeframe}")
            return 1

        # Extract target column
        target_series = df[args.target_column]
        logger.info(f"Using target column: {args.target_column}")

        # Create plots directory using standardized path
        plots_dir = get_plots_path()
        plots_dir.mkdir(parents=True, exist_ok=True)

        # Plot the original time series
        plt.figure(figsize=(12, 6))
        plt.plot(target_series)
        plt.title(f'BTCUSD.a {args.timeframe} - {args.target_column} Price')
        plt.xlabel('Time')
        plt.ylabel('Price')
        plt.savefig(str(plots_dir / f'original_series_{args.timeframe}.png'))
        plt.close()

        # Preprocess the data with feature engineering
        logger.info("Preprocessing data and analyzing time series characteristics...")
        processed_series, preprocess_params = preprocess_data(target_series, df, args.target_column)

        # Check if feature engineering was successful
        use_exog = preprocess_params.get('has_exog', False)
        if use_exog:
            logger.info("Using exogenous variables (feature engineering) for ARIMA modeling")

        # Plot ACF and PACF
        _, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        plot_acf(processed_series.dropna(), lags=40, ax=ax1)
        plot_pacf(processed_series.dropna(), lags=40, ax=ax2)
        plt.tight_layout()
        plt.savefig(str(plots_dir / f'acf_pacf_{args.timeframe}.png'))
        plt.close()

        # Get suggested parameters from preprocessing
        suggested_d = preprocess_params['d']
        has_seasonality = preprocess_params['has_seasonality']
        seasonal_period = preprocess_params['seasonal_period']

        # Use detected seasonality if available
        if has_seasonality and seasonal_period > 0:
            args.use_seasonal = True
            if args.seasonal_m == 0:  # Only override if not explicitly set
                args.seasonal_m = seasonal_period
                logger.info(f"Using detected seasonal period: {seasonal_period}")

        # Split into train and test sets (using the processed series)
        train_series = processed_series.iloc[:-int(len(processed_series) * args.test_size)]
        test_series = processed_series.iloc[-int(len(processed_series) * args.test_size):]

        # Convert to numpy arrays for compatibility
        train_data = train_series.values
        test_data = test_series.values

        logger.info(f"Training data shape: {train_data.shape}, Test data shape: {test_data.shape}")

        # Train ARIMA model
        logger.info(f"Training ARIMA model for BTCUSD.a {args.timeframe}")

        # Prepare exogenous variables if available
        train_exog = None
        test_exog = None

        if use_exog:
            try:
                # Get feature columns and feature DataFrame
                feature_columns = preprocess_params.get('feature_columns', [])
                feature_df = preprocess_params.get('feature_df', None)

                if feature_columns and feature_df is not None:
                    # Select only the most important features to avoid overfitting
                    from sklearn.feature_selection import SelectKBest, f_regression

                    # Prepare data for feature selection
                    X = feature_df[feature_columns].values
                    y = feature_df[args.target_column].values

                    # Select top features
                    k = min(20, len(feature_columns))  # Use at most 20 features
                    selector = SelectKBest(f_regression, k=k)
                    selector.fit(X, y)

                    # Get selected feature indices and names
                    selected_indices = selector.get_support(indices=True)
                    selected_features = [feature_columns[i] for i in selected_indices]

                    logger.info(f"Selected {len(selected_features)} most important features: {', '.join(selected_features[:5])}...")

                    # Get the train/test split indices
                    train_size = len(train_data)
                    test_size = len(test_data)
                    total_size = train_size + test_size

                    # Ensure feature_df has the same length as the combined train and test data
                    if len(feature_df) != total_size:
                        logger.warning(f"Feature DataFrame length ({len(feature_df)}) doesn't match data length ({total_size})")
                        # Adjust feature_df to match the data length
                        if len(feature_df) > total_size:
                            # Trim feature_df to match data length
                            feature_df = feature_df.iloc[:total_size]
                        else:
                            # Can't use features if they're shorter than the data
                            logger.warning("Feature DataFrame is too short, cannot use exogenous variables")
                            use_exog = False
                            train_exog = None
                            test_exog = None
                            return

                    # Split features into train and test sets
                    train_exog = feature_df.iloc[:train_size][selected_features].values
                    test_exog = feature_df.iloc[train_size:train_size+test_size][selected_features].values

                    logger.info(f"Exogenous variables shapes - Train: {train_exog.shape}, Test: {test_exog.shape}")

                    # Verify shapes match
                    if train_exog.shape[0] != train_size:
                        logger.warning(f"Train exog shape ({train_exog.shape[0]}) doesn't match train data shape ({train_size})")
                        use_exog = False
                        train_exog = None
                        test_exog = None
                    elif test_exog.shape[0] != test_size:
                        logger.warning(f"Test exog shape ({test_exog.shape[0]}) doesn't match test data shape ({test_size})")
                        use_exog = False
                        train_exog = None
                        test_exog = None
                    else:
                        logger.info("Exogenous variables shapes match data shapes")
            except Exception as e:
                logger.warning(f"Error preparing exogenous variables: {str(e)}")
                logger.warning("Proceeding without exogenous variables")
                use_exog = False
                train_exog = None
                test_exog = None

        # Check if we should use the ensemble model
        if args.use_ensemble and ENSEMBLE_AVAILABLE:
            logger.info("Using Ensemble ARIMA model for improved performance")

            # Configure the ensemble model
            ensemble_config = {
                'n_models': args.ensemble_models,
                'd': suggested_d,
                'seasonal': args.use_seasonal,
                'seasonal_m': args.seasonal_m if args.seasonal_m > 0 else 12
            }

            # Create and train the ensemble model
            model = EnsembleARIMAModel(config=ensemble_config)
            model.build()

            # Train with exogenous variables if available
            if use_exog and train_exog is not None:
                logger.info("Training ensemble model with exogenous variables")
                model.train(train_data, exog=train_exog)
            else:
                model.train(train_data)

            # Set best parameters for logging
            best_p, best_d, best_q = 0, suggested_d, 0  # Placeholder values
            best_P, best_D, best_Q, best_m = 0, 0, 0, 0

            logger.info(f"Trained Ensemble ARIMA model with {args.ensemble_models} base models")

        elif args.auto_arima:
            logger.info("Using auto_arima to find the best parameters")

            # Set up auto_arima with improved parameters
            auto_arima_kwargs = {
                'start_p': 1,  # Start with at least AR(1)
                'start_q': 1,  # Start with at least MA(1)
                'max_p': 5,
                'max_q': 5,
                'd': suggested_d,  # Use suggested differencing from preprocessing
                'seasonal': args.use_seasonal,
                'trace': True,
                'error_action': 'ignore',
                'suppress_warnings': True,
                'stepwise': False,  # Use a more thorough search
                'information_criterion': 'aic',  # Use AIC for model selection
                'with_intercept': True,  # Include intercept term
                'max_order': 10,  # Limit total order to prevent overfitting
                'n_jobs': -1,  # Use all available cores
                'method': 'lbfgs',  # Use a more robust optimization method
                'maxiter': 50,  # Increase max iterations for better convergence
                'random': True,  # Add randomness to avoid local minima
                'random_state': 42  # For reproducibility
            }

            # Add seasonal parameters if using seasonality
            if args.use_seasonal:
                auto_arima_kwargs.update({
                    'start_P': 0,
                    'start_Q': 0,
                    'max_P': 2,
                    'max_Q': 2,
                    'D': None,
                    'm': args.seasonal_m if args.seasonal_m > 0 else 12
                })

            # Add exogenous variables if available
            if use_exog and train_exog is not None:
                logger.info("Including exogenous variables in auto_arima")
                auto_arima_kwargs['exogenous'] = train_exog

            # Fit the model
            model = auto_arima(train_data, **auto_arima_kwargs)

            # Get the best parameters
            best_p, best_d, best_q = model.order
            best_P, best_D, best_Q, best_m = model.seasonal_order if args.use_seasonal else (0, 0, 0, 0)

            logger.info(f"Best parameters: ARIMA({best_p},{best_d},{best_q})({best_P},{best_D},{best_Q},{best_m})")
        else:
            from statsmodels.tsa.arima.model import ARIMA
            from statsmodels.tsa.statespace.sarimax import SARIMAX

            # Use suggested differencing if not specified
            if args.d is None or args.d < 0:
                args.d = suggested_d
                logger.info(f"Using suggested differencing order: {suggested_d}")

            if args.use_seasonal:
                logger.info(f"Fitting SARIMAX model with order=({args.p},{args.d},{args.q}) and seasonal_order=({args.seasonal_p},{args.seasonal_d},{args.seasonal_q},{args.seasonal_m})")

                # Add exogenous variables if available
                if use_exog and train_exog is not None:
                    logger.info("Including exogenous variables in SARIMAX model")
                    model = SARIMAX(
                        train_data,
                        exog=train_exog,
                        order=(args.p, args.d, args.q),
                        seasonal_order=(args.seasonal_p, args.seasonal_d, args.seasonal_q, args.seasonal_m),
                        enforce_stationarity=False,
                        enforce_invertibility=False
                    ).fit(disp=False)
                else:
                    model = SARIMAX(
                        train_data,
                        order=(args.p, args.d, args.q),
                        seasonal_order=(args.seasonal_p, args.seasonal_d, args.seasonal_q, args.seasonal_m),
                        enforce_stationarity=False,
                        enforce_invertibility=False
                    ).fit(disp=False)
            else:
                logger.info(f"Fitting ARIMA model with order=({args.p},{args.d},{args.q})")

                # Add exogenous variables if available
                if use_exog and train_exog is not None:
                    logger.info("Including exogenous variables in ARIMA model")
                    model = ARIMA(
                        train_data,
                        exog=train_exog,
                        order=(args.p, args.d, args.q)
                    ).fit()
                else:
                    model = ARIMA(
                        train_data,
                        order=(args.p, args.d, args.q)
                    ).fit()

            best_p, best_d, best_q = args.p, args.d, args.q
            best_P, best_D, best_Q, best_m = args.seasonal_p, args.seasonal_d, args.seasonal_q, args.seasonal_m

        # Make predictions
        logger.info("Making predictions on test data")

        # Get in-sample predictions (fitted values) - for potential future use
        # in_sample_pred = None
        # if hasattr(model, 'predict_in_sample'):
        #     in_sample_pred = model.predict_in_sample()
        # elif hasattr(model, 'fittedvalues'):
        #     in_sample_pred = model.fittedvalues

        # Make out-of-sample predictions with exogenous variables if available
        if use_exog and test_exog is not None:
            logger.info("Making predictions with exogenous variables")
            try:
                if args.use_ensemble and ENSEMBLE_AVAILABLE:
                    # For ensemble ARIMA
                    y_pred = model.predict(n_periods=len(test_data), exog=test_exog)
                elif hasattr(model, 'forecast'):
                    # For statsmodels ARIMA/SARIMAX
                    y_pred = model.forecast(steps=len(test_data), exog=test_exog)
                else:
                    # For pmdarima AutoARIMA
                    y_pred = model.predict(n_periods=len(test_data), X=test_exog)
            except Exception as e:
                logger.warning(f"Error making predictions with exogenous variables: {str(e)}")
                logger.warning("Falling back to predictions without exogenous variables")
                y_pred = model.predict(n_periods=len(test_data))
        else:
            # Make predictions without exogenous variables
            y_pred = model.predict(n_periods=len(test_data))

        # Validate prediction shapes before calculating metrics
        logger.info(f"Test data shape: {test_data.shape}, Predictions shape: {y_pred.shape if hasattr(y_pred, 'shape') else len(y_pred)}")

        # Ensure predictions and test data have compatible shapes
        if hasattr(y_pred, 'shape'):
            if len(y_pred.shape) > 1:
                y_pred = y_pred.flatten()

        if hasattr(test_data, 'shape'):
            if len(test_data.shape) > 1:
                test_data_flat = test_data.flatten()
            else:
                test_data_flat = test_data
        else:
            test_data_flat = np.array(test_data)

        # Ensure both arrays have the same length
        min_len = min(len(y_pred), len(test_data_flat))
        if min_len == 0:
            logger.error("No valid predictions or test data available")
            return 1

        y_pred_trimmed = y_pred[:min_len]
        test_data_trimmed = test_data_flat[:min_len]

        logger.info(f"Using {min_len} samples for evaluation (trimmed from {len(test_data_flat)} test samples and {len(y_pred)} predictions)")

        # Calculate metrics with validated data
        mse = mean_squared_error(test_data_trimmed, y_pred_trimmed)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(test_data_trimmed, y_pred_trimmed)
        r2 = r2_score(test_data_trimmed, y_pred_trimmed)

        logger.info(f"Model performance metrics:")
        logger.info(f"MSE: {mse:.2f}")
        logger.info(f"RMSE: {rmse:.2f}")
        logger.info(f"MAE: {mae:.2f}")
        logger.info(f"R2: {r2:.4f}")

        # Calculate additional metrics
        mean_target = np.mean(test_data)
        mean_absolute_percentage_error = np.mean(np.abs((test_data - y_pred) / test_data)) * 100

        logger.info(f"Mean target value: {mean_target:.2f}")
        logger.info(f"Mean Absolute Percentage Error: {mean_absolute_percentage_error:.2f}%")

        # Save model using standardized path
        model_dir = get_model_path(model_name="arima", timeframe=args.timeframe)
        model_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Saving model to standardized path: {model_dir}")

        with open(model_dir / "model.pkl", "wb") as f:
            pickle.dump(model, f)

        # Save last values for future predictions
        last_values = processed_series.iloc[-60:].values  # Save last 60 values
        np.save(model_dir / "last_values.npy", last_values)

        # Save configuration
        config = {
            'model_type': 'arima',
            'symbol': 'BTCUSD.a',
            'timeframe': args.timeframe,
            'target_column': args.target_column,
            'auto_arima': args.auto_arima,
            'order': (best_p, best_d, best_q),
            'seasonal_order': (best_P, best_D, best_Q, best_m) if args.use_seasonal else None,
            'use_seasonal': args.use_seasonal,
            'preprocessing': {
                'suggested_d': suggested_d,
                'has_seasonality': has_seasonality,
                'seasonal_period': seasonal_period
            },
            'metrics': {
                'mse': float(mse),
                'rmse': float(rmse),
                'mae': float(mae),
                'r2': float(r2),
                'mape': float(mean_absolute_percentage_error)
            }
        }

        # Save configuration as JSON
        with open(model_dir / "config.json", "w") as f:
            json.dump(config, f, indent=4)

        logger.info(f"Model saved to {model_dir}")

        # Save metrics to a summary file using standardized path
        metrics_dir = get_metrics_path()
        metrics_dir.mkdir(parents=True, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        summary_file = metrics_dir / f"arima_BTCUSD.a_{args.timeframe}_{timestamp}.json"

        with open(str(summary_file), 'w') as f:
            json.dump({
                'model_type': 'arima',
                'symbol': 'BTCUSD.a',
                'timeframe': args.timeframe,
                'order': (best_p, best_d, best_q),
                'seasonal_order': (best_P, best_D, best_Q, best_m) if args.use_seasonal else None,
                'metrics': {
                    'mse': float(mse),
                    'rmse': float(rmse),
                    'mae': float(mae),
                    'r2': float(r2),
                    'mape': float(mean_absolute_percentage_error)
                }
            }, f, indent=4)

        # Plot predictions vs actual
        plt.figure(figsize=(15, 10))

        # Create a subplot for the full test set
        plt.subplot(2, 1, 1)
        plt.plot(test_series.index, test_data, label='Actual', color='blue')
        plt.plot(test_series.index, y_pred, label='Predicted', color='red', linestyle='--')
        plt.legend()
        plt.title(f'ARIMA Model Predictions vs Actual for {args.timeframe} - Full Test Set')
        plt.xlabel('Time')
        plt.ylabel('Price')

        # Create a subplot for a zoomed-in view (last 100 points)
        plt.subplot(2, 1, 2)
        zoom_length = min(100, len(test_data))
        plt.plot(test_series.index[-zoom_length:], test_data[-zoom_length:], label='Actual', color='blue')
        plt.plot(test_series.index[-zoom_length:], y_pred[-zoom_length:], label='Predicted', color='red', linestyle='--')
        plt.legend()
        plt.title(f'ARIMA Model Predictions vs Actual for {args.timeframe} - Last {zoom_length} Points')
        plt.xlabel('Time')
        plt.ylabel('Price')

        plt.tight_layout()
        plt.savefig(str(plots_dir / f'arima_predictions_{args.timeframe}.png'))
        plt.close()

        # Plot residuals
        plt.figure(figsize=(15, 10))

        # Calculate residuals
        residuals = test_data - y_pred

        # Plot residuals
        plt.subplot(2, 2, 1)
        plt.plot(residuals)
        plt.title('Residuals')
        plt.xlabel('Observation')
        plt.ylabel('Residual')

        # Plot residuals histogram
        plt.subplot(2, 2, 2)
        plt.hist(residuals, bins=30)
        plt.title('Residuals Histogram')
        plt.xlabel('Residual')
        plt.ylabel('Frequency')

        # Plot residuals Q-Q plot
        plt.subplot(2, 2, 3)
        stats.probplot(residuals, dist="norm", plot=plt)
        plt.title('Residuals Q-Q Plot')

        # Plot residuals autocorrelation
        plt.subplot(2, 2, 4)
        plot_acf(residuals, lags=40, ax=plt.gca())
        plt.title('Residuals Autocorrelation')

        plt.tight_layout()
        plt.savefig(str(plots_dir / f'arima_diagnostics_{args.timeframe}.png'))
        plt.close()

        logger.info(f"Metrics saved to {summary_file}")
        logger.info(f"Plots saved to {plots_dir}/arima_predictions_{args.timeframe}.png and {plots_dir}/arima_diagnostics_{args.timeframe}.png")
        logger.info("Training completed successfully.")
        return 0  # Explicitly return success exit code

    except Exception as e:
        logger.error(f"Error training ARIMA model: {str(e)}", exc_info=True)
        return 1  # Return error code on exception

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)  # Explicitly exit with the returned code
