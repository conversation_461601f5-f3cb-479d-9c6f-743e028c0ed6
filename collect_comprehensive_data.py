#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to collect comprehensive data with technical indicators from all terminals.

This script directly uses the DataCollector.collect_data_with_indicators method to collect
data with all technical indicators from all terminals and then merges the results.

Features:
- Collects data from multiple MT5 terminals (default: terminals 1-5)
- Adds technical indicators (SMAs, EMAs, Bollinger Bands, RSI, MACD)
- Merges data from all terminals to create a comprehensive dataset
- Validates the collected data to ensure quality
- Supports multiple timeframes in a single run

Usage examples:
    # Collect 5-year BTCUSD.a data for all timeframes with validation
    python collect_comprehensive_data.py --symbol BTCUSD.a --timeframes M5,M15,M30,H1,H4 --start-date 2020-05-08 --end-date 2025-05-07 --format parquet --validate

    # Collect EURUSD data for specific timeframes from specific terminals
    python collect_comprehensive_data.py --symbol EURUSD --timeframes M5,H1 --terminal-ids 1,2,3 --format parquet

    # Collect GBPUSD data for the last month
    python collect_comprehensive_data.py --symbol GBPUSD --timeframes M15,H4 --start-date 2025-04-01 --format parquet

The collected data includes:
1. Basic price data: time, open, high, low, close, tick_volume, spread, real_volume
2. Moving averages: sma_5, sma_10, sma_20, sma_50, sma_100, ema_5, ema_10, ema_20, ema_50, ema_100
3. Bollinger Bands: bb_middle, bb_std, bb_upper, bb_lower
4. Oscillators: rsi_14, macd, macd_signal, macd_hist

Output:
- Terminal-specific data is saved in {output_dir}/{symbol}/terminal{id}/{symbol}_{timeframe}_processed.{format}
- Merged data is saved in {output_dir}/{symbol}/{symbol}_{timeframe}.{format}
"""

import os
import sys
import logging
import argparse
import pandas as pd
from datetime import datetime
from pathlib import Path  # Used for file path operations
from typing import List, Dict, Optional

from utils.data_collector import DataCollector
from config.unified_config import UnifiedConfigManager
from utils.mt5.mt5_connection_manager import MT5ConnectionManager
from utils.error_handler import ErrorHandler
from config import get_historical_data_path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/collect_comprehensive_data.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def parse_date(date_str: str) -> Optional[datetime]:
    """
    Parse a date string into a datetime object.

    Args:
        date_str: Date string in YYYY-MM-DD format

    Returns:
        Datetime object or None if parsing failed
    """
    try:
        return datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        logger.error(f"Invalid date format: {date_str}. Expected YYYY-MM-DD.")
        return None

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Collect comprehensive data with technical indicators from all terminals')

    # Symbol arguments
    parser.add_argument('--symbol', type=str, required=True, help='Trading symbol (e.g., BTCUSD.a)')

    # Timeframe arguments
    parser.add_argument('--timeframes', type=str, required=True, help='Comma-separated list of timeframes (e.g., M5,H1,H4)')

    # Date arguments
    parser.add_argument('--start-date', type=str, help='Start date for data collection (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='End date for data collection (YYYY-MM-DD)')

    # Output directory
    parser.add_argument('--output-dir', type=str, default=None, help='Output directory for merged data (uses config if not specified)')

    # Format
    parser.add_argument('--format', type=str, choices=['csv', 'parquet'], default='parquet', help='File format to save the data')

    # Terminal IDs
    parser.add_argument('--terminal-ids', type=str, default='1,2,3,4,5', help='Comma-separated list of terminal IDs')

    # Validation
    parser.add_argument('--validate', action='store_true', help='Validate the collected data')

    return parser.parse_args()

def collect_data_from_terminal(
    terminal_id: str,
    symbol: str,
    timeframes: List[str],
    start_date: Optional[datetime],
    end_date: Optional[datetime],
    output_dir: str,
    file_format: str
) -> Dict[str, pd.DataFrame]:
    """
    Collect data from a specific terminal.

    Args:
        terminal_id: Terminal ID
        symbol: Trading symbol
        timeframes: List of timeframes
        start_date: Start date for data collection
        end_date: End date for data collection
        output_dir: Output directory for collected data
        file_format: File format to save the data

    Returns:
        Dict[str, pd.DataFrame]: Dictionary mapping timeframes to DataFrames
    """
    try:
        # Create terminal-specific output directory
        terminal_output_dir = os.path.join(output_dir, f"terminal{terminal_id}")
        os.makedirs(terminal_output_dir, exist_ok=True)

        # Initialize configuration manager
        config_manager = UnifiedConfigManager()

        # Initialize MT5 connection manager
        mt5_manager = MT5ConnectionManager(config_manager)

        # Initialize error handler
        error_handler = ErrorHandler()

        # Initialize data collector
        data_collector = DataCollector(
            mt5_manager=mt5_manager,
            error_handler=error_handler,
            cache_dir=terminal_output_dir
        )

        # Collect data for each timeframe
        result = {}
        for timeframe in timeframes:
            logger.info(f"Collecting {symbol} {timeframe} data from terminal {terminal_id}")

            # Collect data with indicators
            df = data_collector.collect_data_with_indicators(
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                terminal_id=int(terminal_id),
                save_to_file=True,
                file_format=file_format,
                add_indicators=True,
                normalize=False
            )

            if df is not None:
                logger.info(f"Successfully collected {len(df)} rows for {symbol} {timeframe} from terminal {terminal_id}")
                result[timeframe] = df
            else:
                logger.error(f"Failed to collect data for {symbol} {timeframe} from terminal {terminal_id}")

        return result

    except Exception as e:
        logger.error(f"Error collecting data from terminal {terminal_id}: {str(e)}")
        return {}

def merge_data(
    terminal_ids: List[str],
    symbol: str,
    timeframes: List[str],
    output_dir: str,
    file_format: str
) -> Dict[str, bool]:
    """
    Merge data from all terminals.

    Args:
        terminal_ids: List of terminal IDs
        symbol: Trading symbol
        timeframes: List of timeframes
        output_dir: Output directory for merged data
        file_format: File format of the data

    Returns:
        Dict[str, bool]: Dictionary mapping timeframes to success status
    """
    result = {}

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Merge data for each timeframe
    for timeframe in timeframes:
        try:
            # List of dataframes from each terminal
            dfs = []

            # Load data from each terminal
            for terminal_id in terminal_ids:
                # Look in terminal-specific directory
                terminal_dir = os.path.join(output_dir, f"terminal{terminal_id}")
                file_path = os.path.join(terminal_dir, f"{symbol}_{timeframe}_processed.{file_format}")

                if os.path.exists(file_path):
                    try:
                        if file_format == 'csv':
                            df = pd.read_csv(file_path)
                            # Convert time column to datetime
                            if 'time' in df.columns:
                                df['time'] = pd.to_datetime(df['time'])
                        else:  # parquet
                            df = pd.read_parquet(file_path)

                        dfs.append(df)
                        logger.info(f"Loaded {len(df)} rows from {file_path}")
                    except Exception as e:
                        logger.error(f"Error loading {file_path}: {str(e)}")
                else:
                    logger.warning(f"No data file found for terminal {terminal_id}, timeframe {timeframe}")

            if dfs:
                # Concatenate dataframes
                merged_df = pd.concat(dfs)

                # Remove duplicates
                merged_df = merged_df.drop_duplicates(subset=['time'])

                # Sort by time
                merged_df = merged_df.sort_values('time')

                # Save merged data
                merged_file_path = os.path.join(output_dir, f"{symbol}_{timeframe}.{file_format}")
                if file_format == 'csv':
                    merged_df.to_csv(merged_file_path, index=False)
                else:  # parquet
                    merged_df.to_parquet(merged_file_path, index=False)

                logger.info(f"Saved merged data with {len(merged_df)} rows to {merged_file_path}")
                result[timeframe] = True
            else:
                logger.warning(f"No data to merge for {symbol} {timeframe}")
                result[timeframe] = False

        except Exception as e:
            logger.error(f"Error merging data for {symbol} {timeframe}: {str(e)}")
            result[timeframe] = False

    return result

def validate_data(
    symbol: str,
    timeframes: List[str],
    output_dir: str,
    file_format: str
) -> Dict[str, bool]:
    """
    Validate the collected data.

    Args:
        symbol: Trading symbol
        timeframes: List of timeframes
        output_dir: Output directory for merged data
        file_format: File format of the data

    Returns:
        Dict[str, bool]: Dictionary mapping timeframes to validation status
    """
    result = {}

    for timeframe in timeframes:
        try:
            file_path = os.path.join(output_dir, f"{symbol}_{timeframe}.{file_format}")
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                result[timeframe] = False
                continue

            # Load data
            if file_format == 'csv':
                df = pd.read_csv(file_path)
                # Convert time column to datetime
                if 'time' in df.columns:
                    df['time'] = pd.to_datetime(df['time'])
            else:  # parquet
                df = pd.read_parquet(file_path)

            # Check if data is empty
            if df.empty:
                logger.error(f"Data is empty for {symbol} {timeframe}")
                result[timeframe] = False
                continue

            # Check for required columns
            required_columns = ['time', 'open', 'high', 'low', 'close', 'tick_volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"Missing columns for {symbol} {timeframe}: {missing_columns}")
                result[timeframe] = False
                continue

            # Check for technical indicators
            indicator_columns = ['sma_20', 'ema_20', 'rsi_14', 'macd', 'bb_upper', 'bb_lower']
            missing_indicators = [col for col in indicator_columns if col not in df.columns]
            if missing_indicators:
                logger.warning(f"Missing indicators for {symbol} {timeframe}: {missing_indicators}")
                # Don't fail validation for missing indicators, just warn

            # Check for NaN values
            nan_counts = df[required_columns].isna().sum()
            if nan_counts.sum() > 0:
                logger.warning(f"NaN values found for {symbol} {timeframe}: {nan_counts}")
                # Don't fail validation for NaN values, just warn

            # Check for duplicate timestamps
            duplicate_count = df.duplicated(subset=['time']).sum()
            if duplicate_count > 0:
                logger.warning(f"Duplicate timestamps found for {symbol} {timeframe}: {duplicate_count}")
                # Don't fail validation for duplicates, just warn

            # Check for invalid price relationships
            invalid_price_count = ((df['low'] > df['high']) | (df['close'] > df['high']) | (df['close'] < df['low'])).sum()
            if invalid_price_count > 0:
                logger.error(f"Invalid price relationships found for {symbol} {timeframe}: {invalid_price_count}")
                result[timeframe] = False
                continue

            # Check date range
            date_range = f"{df['time'].min()} to {df['time'].max()}"
            logger.info(f"Date range for {symbol} {timeframe}: {date_range}")

            # Check row count
            logger.info(f"Row count for {symbol} {timeframe}: {len(df)}")

            # Check column count
            logger.info(f"Column count for {symbol} {timeframe}: {len(df.columns)}")

            # All checks passed
            logger.info(f"Validation passed for {symbol} {timeframe}")
            result[timeframe] = True

        except Exception as e:
            logger.error(f"Error validating data for {symbol} {timeframe}: {str(e)}")
            result[timeframe] = False

    return result

def main():
    """Main function."""
    args = parse_args()

    # Parse timeframes
    timeframes = args.timeframes.split(',')

    # Parse terminal IDs
    terminal_ids = args.terminal_ids.split(',')

    # Parse dates
    start_date = parse_date(args.start_date) if args.start_date else None
    end_date = parse_date(args.end_date) if args.end_date else None

    # Determine output directory
    if args.output_dir is None:
        base_output_dir = get_historical_data_path()
        logger.info(f"Using configured data directory: {base_output_dir}")
    else:
        base_output_dir = args.output_dir
        logger.info(f"Using specified data directory: {base_output_dir}")

    # Create symbol-specific output directory
    output_dir = os.path.join(str(base_output_dir), args.symbol.lower())
    os.makedirs(output_dir, exist_ok=True)

    # Collect data from each terminal
    for terminal_id in terminal_ids:
        collect_data_from_terminal(
            terminal_id=terminal_id,
            symbol=args.symbol,
            timeframes=timeframes,
            start_date=start_date,
            end_date=end_date,
            output_dir=output_dir,
            file_format=args.format
        )

    # Merge data from all terminals
    # Store results for potential future use (e.g., reporting success/failure)
    merge_results = merge_data(
        terminal_ids=terminal_ids,
        symbol=args.symbol,
        timeframes=timeframes,
        output_dir=output_dir,
        file_format=args.format
    )

    # Validate data if requested
    if args.validate:
        validation_results = validate_data(
            symbol=args.symbol,
            timeframes=timeframes,
            output_dir=output_dir,
            file_format=args.format
        )

        # Print validation summary
        logger.info("Validation summary:")
        for timeframe, result in validation_results.items():
            logger.info(f"{timeframe}: {'PASSED' if result else 'FAILED'}")

    logger.info("Data collection complete")

if __name__ == "__main__":
    main()
