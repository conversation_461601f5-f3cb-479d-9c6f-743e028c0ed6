#!/usr/bin/env python
"""
System Integrity Validation Script

This script validates the entire trading bot system to ensure:
1. Configuration consistency across all files
2. Model path standardization
3. Data structure integrity
4. Terminal ID consistency
5. Component integration
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import pandas as pd

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SystemIntegrityValidator:
    """Validates the entire system for consistency and integrity."""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.info = []
        
    def validate_all(self) -> bool:
        """Run all validation checks."""
        logger.info("Starting comprehensive system integrity validation...")
        
        # Configuration validation
        self.validate_configuration_consistency()
        
        # Model structure validation
        self.validate_model_structure()
        
        # Data structure validation
        self.validate_data_structure()
        
        # Terminal assignments validation
        self.validate_terminal_assignments()
        
        # Dependencies validation
        self.validate_dependencies()
        
        # Report results
        self.report_results()
        
        return len(self.errors) == 0
    
    def validate_configuration_consistency(self):
        """Validate configuration consistency across all config files."""
        logger.info("Validating configuration consistency...")
        
        try:
            # Load main config
            with open('config/config.json', 'r') as f:
                main_config = json.load(f)
            
            # Check terminal ID consistency
            mt5_terminals = main_config.get('mt5', {}).get('terminals', {})
            terminal_pairings = main_config.get('terminal_model_pairings', {})
            
            # Validate terminal IDs are strings
            for terminal_id in mt5_terminals.keys():
                if not isinstance(terminal_id, str):
                    self.errors.append(f"Terminal ID {terminal_id} in mt5.terminals should be string")
            
            for terminal_id in terminal_pairings.keys():
                if not isinstance(terminal_id, str):
                    self.errors.append(f"Terminal ID {terminal_id} in terminal_model_pairings should be string")
            
            # Check if all terminals have model assignments
            for terminal_id in mt5_terminals.keys():
                if terminal_id not in terminal_pairings:
                    self.warnings.append(f"Terminal {terminal_id} has no model assignment")
            
            # Validate model configurations
            models = main_config.get('models', {})
            expected_models = ['lstm', 'arima', 'tft', 'lstm_arima_ensemble', 'tft_arima_ensemble']
            
            for model_name in expected_models:
                if model_name not in models:
                    self.warnings.append(f"Model {model_name} not found in configuration")
                else:
                    model_config = models[model_name]
                    required_fields = ['model_path', 'input_dim', 'output_dim', 'weight', 'FEATURE_COLUMNS']
                    for field in required_fields:
                        if field not in model_config:
                            self.errors.append(f"Model {model_name} missing required field: {field}")
            
            self.info.append("Configuration consistency validation completed")
            
        except Exception as e:
            self.errors.append(f"Error validating configuration: {str(e)}")
    
    def validate_model_structure(self):
        """Validate model directory structure and files."""
        logger.info("Validating model structure...")
        
        try:
            models_dir = Path('models')
            if not models_dir.exists():
                self.errors.append("Models directory does not exist")
                return
            
            # Expected model patterns
            timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
            model_types = ['lstm', 'tft', 'arima']
            symbol = 'BTCUSD.a'
            
            # Check for standard model directories
            for model_type in model_types:
                for timeframe in timeframes:
                    model_dir_name = f"{model_type}_{symbol}_{timeframe}"
                    model_dir = models_dir / model_dir_name
                    
                    if model_dir.exists():
                        self.info.append(f"Found model directory: {model_dir_name}")
                        
                        # Check for required files
                        if model_type == 'arima':
                            required_files = ['model.pkl', 'config.json']
                        else:
                            required_files = ['model.pt', 'scalers.pt', 'config.json']
                        
                        for req_file in required_files:
                            file_path = model_dir / req_file
                            if not file_path.exists():
                                self.warnings.append(f"Missing file: {model_dir_name}/{req_file}")
                    else:
                        self.warnings.append(f"Missing model directory: {model_dir_name}")
            
            # Check for ensemble model directories
            ensemble_types = ['tft_arima', 'lstm_arima']
            for ensemble_type in ensemble_types:
                for timeframe in timeframes:
                    ensemble_dir_name = f"{ensemble_type}_{symbol}_{timeframe}"
                    ensemble_dir = models_dir / ensemble_dir_name
                    
                    if ensemble_dir.exists():
                        self.info.append(f"Found ensemble directory: {ensemble_dir_name}")
                    else:
                        self.warnings.append(f"Missing ensemble directory: {ensemble_dir_name}")
            
            self.info.append("Model structure validation completed")
            
        except Exception as e:
            self.errors.append(f"Error validating model structure: {str(e)}")
    
    def validate_data_structure(self):
        """Validate data directory structure and files."""
        logger.info("Validating data structure...")
        
        try:
            data_dir = Path('data')
            if not data_dir.exists():
                self.errors.append("Data directory does not exist")
                return
            
            # Check for terminal-specific data directories
            for terminal_id in ['1', '2', '3', '4', '5']:
                terminal_dir = data_dir / f'terminal_{terminal_id}'
                if terminal_dir.exists():
                    self.info.append(f"Found terminal data directory: terminal_{terminal_id}")
                    
                    # Check for timeframe subdirectories
                    timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
                    for timeframe in timeframes:
                        tf_dir = terminal_dir / timeframe
                        if tf_dir.exists():
                            self.info.append(f"Found timeframe directory: terminal_{terminal_id}/{timeframe}")
                        else:
                            self.warnings.append(f"Missing timeframe directory: terminal_{terminal_id}/{timeframe}")
                else:
                    self.warnings.append(f"Missing terminal data directory: terminal_{terminal_id}")
            
            # Check for historical data
            historical_dir = data_dir / 'historical' / 'btcusd.a'
            if historical_dir.exists():
                self.info.append("Found historical data directory")
                
                # Check for parquet files
                timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
                for timeframe in timeframes:
                    parquet_file = historical_dir / f'BTCUSD.a_{timeframe}.parquet'
                    if parquet_file.exists():
                        self.info.append(f"Found historical data file: BTCUSD.a_{timeframe}.parquet")
                        
                        # Validate file content
                        try:
                            df = pd.read_parquet(parquet_file)
                            required_columns = ['open', 'high', 'low', 'close', 'real_volume', 'time']
                            missing_cols = [col for col in required_columns if col not in df.columns]
                            if missing_cols:
                                self.warnings.append(f"Missing columns in {timeframe} data: {missing_cols}")
                            else:
                                self.info.append(f"Data file {timeframe} has all required columns")
                        except Exception as e:
                            self.warnings.append(f"Could not validate {timeframe} data file: {str(e)}")
                    else:
                        self.warnings.append(f"Missing historical data file: BTCUSD.a_{timeframe}.parquet")
            else:
                self.warnings.append("Missing historical data directory")
            
            self.info.append("Data structure validation completed")
            
        except Exception as e:
            self.errors.append(f"Error validating data structure: {str(e)}")
    
    def validate_terminal_assignments(self):
        """Validate terminal model assignments."""
        logger.info("Validating terminal assignments...")
        
        try:
            # Expected assignments based on the system design
            expected_assignments = {
                '1': 'arima',
                '2': 'lstm', 
                '3': 'tft',
                '4': 'lstm_arima_ensemble',
                '5': 'tft_arima_ensemble'
            }
            
            # Load config
            with open('config/config.json', 'r') as f:
                config = json.load(f)
            
            terminal_pairings = config.get('terminal_model_pairings', {})
            
            for terminal_id, expected_model in expected_assignments.items():
                if terminal_id in terminal_pairings:
                    actual_model = terminal_pairings[terminal_id].get('primary_model')
                    if actual_model == expected_model:
                        self.info.append(f"Terminal {terminal_id} correctly assigned to {expected_model}")
                    else:
                        self.warnings.append(f"Terminal {terminal_id} assigned to {actual_model}, expected {expected_model}")
                else:
                    self.errors.append(f"Terminal {terminal_id} has no model assignment")
            
            self.info.append("Terminal assignments validation completed")
            
        except Exception as e:
            self.errors.append(f"Error validating terminal assignments: {str(e)}")
    
    def validate_dependencies(self):
        """Validate that all required dependencies are available."""
        logger.info("Validating dependencies...")
        
        try:
            # Check for required Python modules
            required_modules = [
                'torch', 'tensorflow', 'pandas', 'numpy', 'sklearn',
                'MetaTrader5', 'pmdarima', 'pytorch_lightning'
            ]
            
            for module in required_modules:
                try:
                    __import__(module)
                    self.info.append(f"Module {module} is available")
                except ImportError:
                    self.warnings.append(f"Module {module} is not available")
            
            # Check for required directories
            required_dirs = ['logs', 'metrics', 'plots', 'models', 'data']
            for dir_name in required_dirs:
                if Path(dir_name).exists():
                    self.info.append(f"Directory {dir_name} exists")
                else:
                    self.warnings.append(f"Directory {dir_name} does not exist")
            
            self.info.append("Dependencies validation completed")
            
        except Exception as e:
            self.errors.append(f"Error validating dependencies: {str(e)}")
    
    def report_results(self):
        """Report validation results."""
        logger.info("\n" + "="*80)
        logger.info("SYSTEM INTEGRITY VALIDATION RESULTS")
        logger.info("="*80)
        
        if self.errors:
            logger.error(f"\nERRORS FOUND ({len(self.errors)}):")
            for i, error in enumerate(self.errors, 1):
                logger.error(f"  {i}. {error}")
        
        if self.warnings:
            logger.warning(f"\nWARNINGS ({len(self.warnings)}):")
            for i, warning in enumerate(self.warnings, 1):
                logger.warning(f"  {i}. {warning}")
        
        if self.info:
            logger.info(f"\nSUCCESSFUL VALIDATIONS ({len(self.info)}):")
            for i, info in enumerate(self.info, 1):
                logger.info(f"  {i}. {info}")
        
        logger.info("\n" + "="*80)
        if self.errors:
            logger.error("VALIDATION FAILED - Please fix the errors above")
            return False
        else:
            logger.info("VALIDATION PASSED - System integrity is good")
            return True

def main():
    """Main function to run system validation."""
    validator = SystemIntegrityValidator()
    success = validator.validate_all()
    
    if success:
        logger.info("System is ready for production use")
        return 0
    else:
        logger.error("System has issues that need to be resolved")
        return 1

if __name__ == "__main__":
    exit(main())
