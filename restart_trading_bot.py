#!/usr/bin/env python3
"""
Trading Bot Restart Script

This script safely restarts the trading bot to apply all critical fixes.
"""

import sys
import logging
import subprocess
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def restart_trading_bot():
    """Restart the trading bot with all fixes applied."""
    logger.info("🔄 RESTARTING TRADING BOT WITH CRITICAL FIXES...")
    
    print("\n" + "="*80)
    print("🤖 TRADING BOT RESTART - CRITICAL FIXES APPLIED")
    print("="*80)
    
    print("\n✅ FIXES APPLIED:")
    print("   • Fixed profit/loss tracking system")
    print("   • Corrected signal generation bias (was 100% sell)")
    print("   • Resolved ARIMA model scaling issues")
    print("   • Added real-time trade monitoring")
    print("   • Enhanced performance metrics")
    
    print("\n🎯 EXPECTED IMPROVEMENTS:")
    print("   • Balanced buy/sell signals (instead of 100% sell)")
    print("   • Real-time profit/loss tracking")
    print("   • Reduced model prediction errors")
    print("   • Better system stability")
    
    print("\n🚨 CRITICAL: Monitor the following for 24 hours:")
    print("   • Signal distribution (buy vs sell ratio)")
    print("   • Actual profit/loss tracking")
    print("   • Error rate reduction")
    print("   • Trading performance improvement")
    
    print("\n📊 To monitor performance, run:")
    print("   python generate_performance_report.py")
    
    print("\n" + "="*80)
    
    # Check if main.py exists
    main_script = Path("main.py")
    if not main_script.exists():
        logger.error("❌ main.py not found. Cannot restart trading bot.")
        return False
    
    try:
        logger.info("🔄 Starting trading bot with fixes...")
        print("\n🚀 STARTING TRADING BOT...")
        
        # Start the trading bot
        result = subprocess.run([
            sys.executable, "main.py"
        ], capture_output=False, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Trading bot started successfully")
            return True
        else:
            logger.error(f"❌ Trading bot failed to start. Return code: {result.returncode}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error starting trading bot: {str(e)}")
        return False

def main():
    """Main function."""
    print("🤖 Trading Bot Restart Script")
    print("This will restart the bot with all critical fixes applied.")
    
    # Ask for confirmation
    response = input("\n⚠️  Do you want to restart the trading bot? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        success = restart_trading_bot()
        if success:
            print("\n✅ Trading bot restarted successfully!")
            print("📊 Monitor performance with: python generate_performance_report.py")
        else:
            print("\n❌ Failed to restart trading bot. Check logs for details.")
    else:
        print("\n❌ Restart cancelled.")

if __name__ == "__main__":
    main()
