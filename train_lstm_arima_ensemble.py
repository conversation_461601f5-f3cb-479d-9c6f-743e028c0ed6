#!/usr/bin/env python
"""
LSTM + ARIMA Ensemble Training Script

This script creates and trains LSTM+ARIMA ensemble models for all timeframes,
saving the actual ensemble model files to the lstm_arima_BTCUSD.a_* directories.
"""

import os
import sys
import json
import logging
import argparse
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.lstm_arima_ensemble_model import LSTMARIMAEnsembleModel
from config.unified_config import get_model_path, get_metrics_path
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def train_ensemble_for_timeframe(timeframe: str, symbol: str = "BTCUSD.a") -> Dict[str, Any]:
    """
    Train LSTM+ARIMA ensemble for a specific timeframe.
    
    Args:
        timeframe: Trading timeframe (M5, M15, M30, H1, H4)
        symbol: Trading symbol
        
    Returns:
        Dictionary containing training results and metrics
    """
    try:
        logger.info(f"🚀 Training LSTM+ARIMA ensemble for {timeframe}")
        
        # Create ensemble model
        ensemble = LSTMARIMAEnsembleModel(timeframe=timeframe, symbol=symbol)
        
        # Check if component models exist
        if not ensemble.lstm_model_path.exists():
            logger.error(f"❌ LSTM model not found for {timeframe}: {ensemble.lstm_model_path}")
            return None
            
        if not ensemble.arima_model_path.exists():
            logger.error(f"❌ ARIMA model not found for {timeframe}: {ensemble.arima_model_path}")
            return None
        
        logger.info(f"✓ Component models found for {timeframe}")
        
        # Train ensemble (loads components and optimizes weights)
        if not ensemble.train(optimize_weights=True):
            logger.error(f"❌ Failed to train ensemble for {timeframe}")
            return None
        
        logger.info(f"✓ Ensemble training completed for {timeframe}")
        
        # Save ensemble model
        if not ensemble.save():
            logger.error(f"❌ Failed to save ensemble for {timeframe}")
            return None
            
        logger.info(f"✓ Ensemble model saved for {timeframe}")
        
        # Create training results
        results = {
            'timeframe': timeframe,
            'symbol': symbol,
            'model_type': 'lstm_arima_ensemble',
            'weights': ensemble.weights,
            'lstm_model_path': str(ensemble.lstm_model_path),
            'arima_model_path': str(ensemble.arima_model_path),
            'ensemble_model_path': str(ensemble.ensemble_model_path),
            'training_timestamp': datetime.now().isoformat(),
            'status': 'success'
        }
        
        # Save training metrics
        metrics_dir = get_metrics_path()
        metrics_dir.mkdir(parents=True, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        metrics_file = metrics_dir / f"lstm_arima_ensemble_training_{timeframe}_{timestamp}.json"
        
        with open(metrics_file, 'w') as f:
            json.dump(results, f, indent=4)
        
        logger.info(f"✅ Training metrics saved: {metrics_file}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Error training ensemble for {timeframe}: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def validate_ensemble_model(timeframe: str, symbol: str = "BTCUSD.a") -> Dict[str, Any]:
    """
    Validate a trained ensemble model.
    
    Args:
        timeframe: Trading timeframe
        symbol: Trading symbol
        
    Returns:
        Validation results
    """
    try:
        logger.info(f"🔍 Validating ensemble model for {timeframe}")
        
        # Load ensemble model
        ensemble_path = get_model_path(model_name="lstm_arima", timeframe=timeframe)
        ensemble = LSTMARIMAEnsembleModel.load(ensemble_path, timeframe=timeframe, symbol=symbol)
        
        if ensemble is None:
            logger.error(f"❌ Failed to load ensemble model for {timeframe}")
            return None
        
        logger.info(f"✓ Ensemble model loaded successfully for {timeframe}")
        
        # Basic validation
        validation_results = {
            'timeframe': timeframe,
            'model_loaded': True,
            'weights': ensemble.weights,
            'lstm_model_available': ensemble.lstm_model is not None,
            'arima_model_available': ensemble.arima_model is not None,
            'is_trained': ensemble.is_trained,
            'validation_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"✅ Validation completed for {timeframe}")
        return validation_results
        
    except Exception as e:
        logger.error(f"❌ Error validating ensemble for {timeframe}: {str(e)}")
        return None

def train_all_ensembles(timeframes: List[str] = None, symbol: str = "BTCUSD.a") -> Dict[str, Any]:
    """
    Train LSTM+ARIMA ensembles for all timeframes.
    
    Args:
        timeframes: List of timeframes to train (default: all)
        symbol: Trading symbol
        
    Returns:
        Summary of training results
    """
    if timeframes is None:
        timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
    
    logger.info(f"🎯 Training LSTM+ARIMA ensembles for timeframes: {timeframes}")
    
    results = {
        'training_started': datetime.now().isoformat(),
        'timeframes': timeframes,
        'symbol': symbol,
        'results': {},
        'summary': {
            'total': len(timeframes),
            'successful': 0,
            'failed': 0
        }
    }
    
    for timeframe in timeframes:
        logger.info(f"\n{'='*60}")
        logger.info(f"TRAINING ENSEMBLE FOR {timeframe}")
        logger.info(f"{'='*60}")
        
        # Train ensemble
        training_result = train_ensemble_for_timeframe(timeframe, symbol)
        
        if training_result:
            # Validate ensemble
            validation_result = validate_ensemble_model(timeframe, symbol)
            
            results['results'][timeframe] = {
                'training': training_result,
                'validation': validation_result,
                'status': 'success'
            }
            results['summary']['successful'] += 1
            logger.info(f"✅ {timeframe} ensemble training: SUCCESS")
        else:
            results['results'][timeframe] = {
                'training': None,
                'validation': None,
                'status': 'failed'
            }
            results['summary']['failed'] += 1
            logger.error(f"❌ {timeframe} ensemble training: FAILED")
    
    results['training_completed'] = datetime.now().isoformat()
    
    # Save overall results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = Path(f"lstm_arima_ensemble_training_summary_{timestamp}.json")
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=4)
    
    # Print summary
    logger.info(f"\n{'='*60}")
    logger.info(f"ENSEMBLE TRAINING SUMMARY")
    logger.info(f"{'='*60}")
    logger.info(f"Total timeframes: {results['summary']['total']}")
    logger.info(f"Successful: {results['summary']['successful']}")
    logger.info(f"Failed: {results['summary']['failed']}")
    logger.info(f"Success rate: {results['summary']['successful']/results['summary']['total']*100:.1f}%")
    logger.info(f"Results saved: {results_file}")
    
    if results['summary']['successful'] == results['summary']['total']:
        logger.info("🎉 ALL ENSEMBLE MODELS TRAINED SUCCESSFULLY!")
    else:
        logger.warning(f"⚠️  {results['summary']['failed']} ensemble(s) failed to train")
    
    return results

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Train LSTM+ARIMA Ensemble Models')
    parser.add_argument('--timeframe', type=str, help='Specific timeframe to train (M5, M15, M30, H1, H4)')
    parser.add_argument('--symbol', type=str, default='BTCUSD.a', help='Trading symbol')
    parser.add_argument('--validate-only', action='store_true', help='Only validate existing models')
    
    args = parser.parse_args()
    
    try:
        if args.validate_only:
            # Validation mode
            timeframes = [args.timeframe] if args.timeframe else ['M5', 'M15', 'M30', 'H1', 'H4']
            
            logger.info("🔍 VALIDATION MODE")
            for timeframe in timeframes:
                result = validate_ensemble_model(timeframe, args.symbol)
                if result:
                    logger.info(f"✅ {timeframe}: Validation passed")
                else:
                    logger.error(f"❌ {timeframe}: Validation failed")
        else:
            # Training mode
            if args.timeframe:
                # Train single timeframe
                logger.info(f"🎯 Training ensemble for {args.timeframe}")
                result = train_ensemble_for_timeframe(args.timeframe, args.symbol)
                
                if result:
                    logger.info("✅ Training completed successfully")
                    
                    # Validate
                    validation = validate_ensemble_model(args.timeframe, args.symbol)
                    if validation:
                        logger.info("✅ Validation passed")
                    else:
                        logger.error("❌ Validation failed")
                else:
                    logger.error("❌ Training failed")
            else:
                # Train all timeframes
                results = train_all_ensembles(symbol=args.symbol)
                
                if results['summary']['successful'] == results['summary']['total']:
                    logger.info("🎉 ALL ENSEMBLE TRAINING COMPLETED SUCCESSFULLY!")
                    return 0
                else:
                    logger.error(f"❌ {results['summary']['failed']} ensemble(s) failed")
                    return 1
                    
    except Exception as e:
        logger.error(f"❌ Error in main: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
