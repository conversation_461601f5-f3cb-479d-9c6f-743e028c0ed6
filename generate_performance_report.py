#!/usr/bin/env python3
"""
Performance Report Generator

This script generates a comprehensive performance report for the trading bot,
analyzing profit/loss, trade statistics, and identifying issues.
"""

import sys
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# Import unified configuration
from config import get_monitoring_path, get_logs_path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformanceReportGenerator:
    """Generate comprehensive performance reports for the trading bot."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.logs_dir = get_logs_path()
        self.monitoring_dir = get_monitoring_path()
        
    def analyze_recent_logs(self, days: int = 5) -> Dict[str, Any]:
        """Analyze recent trading logs for performance data."""
        logger.info(f"Analyzing logs from the last {days} days...")
        
        analysis = {
            'total_trades': 0,
            'buy_trades': 0,
            'sell_trades': 0,
            'trades_by_terminal': {},
            'profit_loss_data': [],
            'errors': [],
            'warnings': [],
            'model_issues': []
        }
        
        # Analyze main log file
        main_log = self.logs_dir / "main.log"
        if main_log.exists():
            try:
                with open(main_log, 'r', encoding='utf-8') as f:
                    for line in f:
                        # Parse trade executions
                        if "Trade executed:" in line and "BTCUSD.a" in line:
                            analysis['total_trades'] += 1
                            if "buy" in line.lower():
                                analysis['buy_trades'] += 1
                            elif "sell" in line.lower():
                                analysis['sell_trades'] += 1
                                
                            # Extract terminal ID
                            if "[" in line and "]" in line:
                                terminal_id = line.split("[")[1].split("]")[0]
                                if terminal_id not in analysis['trades_by_terminal']:
                                    analysis['trades_by_terminal'][terminal_id] = 0
                                analysis['trades_by_terminal'][terminal_id] += 1
                        
                        # Parse profit/loss data
                        if "profit_loss" in line and "0.0" not in line:
                            analysis['profit_loss_data'].append(line.strip())
                        
                        # Parse errors
                        if "ERROR" in line:
                            analysis['errors'].append(line.strip())
                        
                        # Parse warnings
                        if "WARNING" in line:
                            analysis['warnings'].append(line.strip())
                        
                        # Parse model issues
                        if "Failed to scale exogenous" in line:
                            analysis['model_issues'].append(line.strip())
                            
            except Exception as e:
                logger.error(f"Error reading main log: {str(e)}")
        
        return analysis
    
    def analyze_monitoring_data(self) -> Dict[str, Any]:
        """Analyze monitoring output data."""
        logger.info("Analyzing monitoring data...")
        
        monitoring_data = {
            'terminal_metrics': {},
            'model_performance': {},
            'system_health': {}
        }
        
        # Check terminal metrics
        for terminal_dir in self.monitoring_dir.glob("terminal_*"):
            if terminal_dir.is_dir():
                terminal_id = terminal_dir.name
                metrics_file = terminal_dir / "metrics_history.json"
                
                if metrics_file.exists():
                    try:
                        with open(metrics_file, 'r') as f:
                            metrics = json.load(f)
                            
                        # Get latest metrics
                        if metrics:
                            latest = metrics[-1]
                            monitoring_data['terminal_metrics'][terminal_id] = {
                                'total_trades': latest.get('total_trades', 0),
                                'winning_trades': latest.get('winning_trades', 0),
                                'losing_trades': latest.get('losing_trades', 0),
                                'total_profit': latest.get('total_profit', 0.0),
                                'max_drawdown': latest.get('max_drawdown', 0.0),
                                'timestamp': latest.get('timestamp', 'unknown')
                            }
                    except Exception as e:
                        logger.error(f"Error reading {metrics_file}: {str(e)}")
        
        return monitoring_data
    
    def identify_issues(self, log_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify critical issues from the analysis."""
        issues = []
        
        # Issue 1: Only sell signals
        if log_analysis['sell_trades'] > 0 and log_analysis['buy_trades'] == 0:
            issues.append({
                'severity': 'CRITICAL',
                'category': 'Signal Generation',
                'issue': 'Only SELL signals generated',
                'description': f"Generated {log_analysis['sell_trades']} sell signals but 0 buy signals",
                'impact': 'Creates directional bias, will lose money in trending markets',
                'recommendation': 'Fix ensemble prediction thresholds in signal_generator.py'
            })
        
        # Issue 2: No profit tracking
        if log_analysis['total_trades'] > 0 and len(log_analysis['profit_loss_data']) == 0:
            issues.append({
                'severity': 'CRITICAL',
                'category': 'Profit Tracking',
                'issue': 'No profit/loss data recorded',
                'description': f"Executed {log_analysis['total_trades']} trades but no P&L tracked",
                'impact': 'Cannot measure performance or calculate actual losses',
                'recommendation': 'Implement proper trade closure monitoring in executor.py'
            })
        
        # Issue 3: Model scaling issues
        if len(log_analysis['model_issues']) > 10:
            issues.append({
                'severity': 'HIGH',
                'category': 'Model Performance',
                'issue': 'ARIMA model scaling failures',
                'description': f"Found {len(log_analysis['model_issues'])} scaling warnings",
                'impact': 'ARIMA model producing unreliable predictions',
                'recommendation': 'Fix feature scaler initialization in ensemble_arima_model.py'
            })
        
        # Issue 4: High error rate
        if len(log_analysis['errors']) > 50:
            issues.append({
                'severity': 'HIGH',
                'category': 'System Stability',
                'issue': 'High error rate',
                'description': f"Found {len(log_analysis['errors'])} errors in recent logs",
                'impact': 'System instability affecting trading performance',
                'recommendation': 'Review error logs and fix underlying issues'
            })
        
        return issues
    
    def generate_report(self, days: int = 5) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        logger.info("Generating comprehensive performance report...")
        
        # Analyze data
        log_analysis = self.analyze_recent_logs(days)
        monitoring_data = self.analyze_monitoring_data()
        issues = self.identify_issues(log_analysis)
        
        # Calculate performance metrics
        total_trades = log_analysis['total_trades']
        sell_bias = (log_analysis['sell_trades'] / total_trades * 100) if total_trades > 0 else 0
        
        # Generate report
        report = {
            'report_timestamp': datetime.now().isoformat(),
            'analysis_period_days': days,
            'summary': {
                'total_trades_executed': total_trades,
                'buy_signals': log_analysis['buy_trades'],
                'sell_signals': log_analysis['sell_trades'],
                'sell_bias_percentage': round(sell_bias, 2),
                'terminals_active': len(log_analysis['trades_by_terminal']),
                'critical_issues_found': len([i for i in issues if i['severity'] == 'CRITICAL']),
                'total_errors': len(log_analysis['errors']),
                'total_warnings': len(log_analysis['warnings'])
            },
            'trading_activity': {
                'trades_by_terminal': log_analysis['trades_by_terminal'],
                'signal_distribution': {
                    'buy': log_analysis['buy_trades'],
                    'sell': log_analysis['sell_trades']
                }
            },
            'profit_loss': {
                'tracked_pnl_entries': len(log_analysis['profit_loss_data']),
                'pnl_data_sample': log_analysis['profit_loss_data'][:5]  # First 5 entries
            },
            'system_health': {
                'error_count': len(log_analysis['errors']),
                'warning_count': len(log_analysis['warnings']),
                'model_issues': len(log_analysis['model_issues'])
            },
            'identified_issues': issues,
            'monitoring_data': monitoring_data,
            'recommendations': self._generate_recommendations(issues)
        }
        
        return report
    
    def _generate_recommendations(self, issues: List[Dict[str, Any]]) -> List[str]:
        """Generate actionable recommendations based on identified issues."""
        recommendations = []
        
        critical_issues = [i for i in issues if i['severity'] == 'CRITICAL']
        
        if critical_issues:
            recommendations.append("🚨 IMMEDIATE ACTION REQUIRED:")
            for issue in critical_issues:
                recommendations.append(f"   • {issue['recommendation']}")
        
        recommendations.extend([
            "",
            "📊 PERFORMANCE IMPROVEMENTS:",
            "   • Implement real-time profit/loss tracking",
            "   • Add trade closure monitoring",
            "   • Fix signal generation bias",
            "   • Improve model prediction reliability",
            "",
            "🔧 SYSTEM MAINTENANCE:",
            "   • Review and fix error logs",
            "   • Optimize model scaling processes",
            "   • Implement better error handling",
            "   • Add automated health checks"
        ])
        
        return recommendations
    
    def save_report(self, report: Dict[str, Any], filename: Optional[str] = None) -> str:
        """Save report to file."""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"performance_report_{timestamp}.json"
        
        report_path = self.project_root / filename
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Report saved to: {report_path}")
        return str(report_path)
    
    def print_summary(self, report: Dict[str, Any]) -> None:
        """Print a human-readable summary of the report."""
        print("\n" + "="*80)
        print("🤖 TRADING BOT PERFORMANCE REPORT")
        print("="*80)
        
        summary = report['summary']
        print(f"📅 Analysis Period: {report['analysis_period_days']} days")
        print(f"📊 Total Trades: {summary['total_trades_executed']}")
        print(f"📈 Buy Signals: {summary['buy_signals']}")
        print(f"📉 Sell Signals: {summary['sell_signals']}")
        print(f"⚖️  Sell Bias: {summary['sell_bias_percentage']}%")
        print(f"🖥️  Active Terminals: {summary['terminals_active']}")
        print(f"🚨 Critical Issues: {summary['critical_issues_found']}")
        print(f"❌ Total Errors: {summary['total_errors']}")
        print(f"⚠️  Total Warnings: {summary['total_warnings']}")
        
        if report['identified_issues']:
            print("\n🔍 IDENTIFIED ISSUES:")
            for issue in report['identified_issues']:
                print(f"   {issue['severity']}: {issue['issue']}")
                print(f"      Impact: {issue['impact']}")
        
        print("\n💡 RECOMMENDATIONS:")
        for rec in report['recommendations']:
            print(f"   {rec}")
        
        print("\n" + "="*80)

def main():
    """Main function."""
    generator = PerformanceReportGenerator()
    
    # Generate report
    report = generator.generate_report(days=5)
    
    # Save report
    report_path = generator.save_report(report)
    
    # Print summary
    generator.print_summary(report)
    
    print(f"\n📄 Full report saved to: {report_path}")

if __name__ == "__main__":
    main()
