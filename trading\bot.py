"""
Main trading bot module that coordinates all components.
"""
import sys
from pathlib import Path
import pandas as pd
from typing import Dict, Optional, Any
import logging
from datetime import datetime
import time
import psutil
import threading
import gc
import os
from utils.enhanced_error_handler import <PERSON>han<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON><PERSON><PERSON>, ErrorSeverity
from utils.circuit_breaker import Circuit<PERSON>reaker, CircuitOpenError, CircuitState
from utils.model_manager import ModelManager
from utils.thread_manager import ThreadManager
from utils.data_preprocessor import DataPreprocessor
from trading.strategy import TradingStrategy
from trading.executor import TradeExecutor
from trading.signal_generator import SignalGenerator
from monitoring.progress import ProgressVisualizer
from config.unified_config import UnifiedConfigManager as ConfigurationManager
from utils.mt5.mt5_connection_manager import MT5ConnectionManager
from utils.comprehensive_logging import (
    get_terminal_logger, log_trade, log_signal,
    log_terminal_error, log_terminal_warning, update_metrics
)
from utils.terminal_manager import get_terminal_manager

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Import data_collector explicitly if needed, otherwise rely on mt5_manager
# from trading.data_collector import DataCollector # Ambiguous, MT5DataCollector imported above

# Removed: from models.model_registry import ModelRegistry # Not needed
# Removed: from config.config_manager import ConfigurationManager # Imported above

# Removed global config loading - should be passed in
# trading_config = config_service.get_trading_config()
# monitoring_config = config_service.get_monitoring_config()
# model_config = config_service.get_model_config()

logger = logging.getLogger(__name__)

# Removed internal ModelRegistry class definition (lines 72-121)

class TradingBot:
    """
    Main trading bot module responsible for coordinating trading activities for a specific terminal.
    Orchestrates data collection, model prediction, signal generation, and trade execution.
    """

    def __init__(self,
                 config_manager: ConfigurationManager, # Passed in
                 error_handler: EnhancedErrorHandler, # Passed in
                 thread_manager: ThreadManager, # Passed in
                 mt5_manager: MT5ConnectionManager, # Passed in
                 terminal_id: str, # Passed in
                 visualizer: Optional[ProgressVisualizer] = None,
                 memory_manager = None):
        """
        Initialize the trading bot for a specific terminal.

        Args:
            config_manager: Singleton ConfigurationManager instance.
            error_handler: Shared ErrorHandler instance.
            thread_manager: Shared ThreadManager instance.
            mt5_manager: Shared MT5ConnectionManager instance.
            terminal_id: The specific terminal ID this bot instance manages.
            visualizer: Optional shared ProgressVisualizer instance.
        """
        self.config_manager = config_manager
        self.error_handler = error_handler
        self.thread_manager = thread_manager
        self.mt5_manager = mt5_manager
        self.terminal_id = terminal_id
        self.visualizer = visualizer
        self.memory_manager = memory_manager

        # Normalize terminal ID for consistency
        try:
            from utils.common import normalize_terminal_id
            self.terminal_id = normalize_terminal_id(self.terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            self.terminal_id = str(self.terminal_id)

        self.config = self.config_manager.get_config() # Get the main TradingConfig
        self.strategy_config = self.config_manager.get_strategy_config() # Get strategy specifics

        # Determine primary timeframe for this bot instance (can be adjusted if bot needs multi-TF logic)
        # For now, assume first timeframe in strategy config is the primary one
        if not self.strategy_config.timeframes:
            raise ValueError(f"No timeframes defined in strategy config for terminal {self.terminal_id}")
        self.primary_timeframe = self.strategy_config.timeframes[0]

        # Determine primary model for this terminal based on terminal model pairings
        self.primary_model = self._get_primary_model_for_terminal()

        logger.info(f"Initializing trading bot for Terminal ID: {self.terminal_id}")
        logger.info(f"Primary Timeframe: {self.primary_timeframe}")
        logger.info(f"Primary Model: {self.primary_model}")

        # Setup terminal-specific logging
        try:
            self.terminal_logger = get_terminal_logger(self.terminal_id)
            self.terminal_logger.info(f"Trading bot initialization started for terminal {self.terminal_id}", extra={'terminal_id': self.terminal_id})

            # Register with terminal manager
            terminal_manager = get_terminal_manager()
            terminal_manager.register_bot(self.terminal_id, self)

            logger.info(f"Using configuration for symbol: {self.strategy_config.symbol}")
            self.terminal_logger.info(f"Symbol: {self.strategy_config.symbol}, Timeframe: {self.primary_timeframe}", extra={'terminal_id': self.terminal_id})
        except Exception as e:
            logger.warning(f"Failed to setup comprehensive logging for terminal {self.terminal_id}: {str(e)}")
            # Continue without comprehensive logging
            self.terminal_logger = logger

        # BTCUSD.a specific configurations - Consider moving to config file if generalizable
        # self.point_value = 0.1
        # self.min_lot_size = 0.01
        # self.max_lot_size = 1.0
        # self.leverage = 1

        # Memory Manager is likely global, passed in or retrieved if needed
        # self.memory_manager = MemoryManager(...) # Assuming global/passed in

        # Data Collector - uses the passed mt5_manager
        # self.data_collector = DataCollector(...) # Assuming MT5DataCollector used via mt5_manager

        # Data Preprocessor
        # Create a simple config dictionary with the necessary parameters
        preprocessor_config = {
            'strategy': {
                'sequence_length': self.strategy_config.sequence_length,
                'batch_size': 32,
                'outlier_std_threshold': 3.0,
                'use_trend_indicators': True,
                'use_momentum_indicators': True,
                'use_volatility_indicators': True,
                'use_volume_indicators': True,
                'use_time_features': True
            },
            'scaling': {
                'price_columns': ['open', 'high', 'low', 'close'],
                'volume_columns': ['volume']
            },
            'preprocessor_max_batch_size': 10000
        }
        self.data_preprocessor = DataPreprocessor(config=preprocessor_config)

        # Initialize thread attributes early to prevent AttributeError
        self.running_thread = None
        self.stop_event = threading.Event()

        # Instantiate the context-specific ModelManager
        logger.info(f"Initializing ModelManager for Terminal {self.terminal_id}, Timeframe {self.primary_timeframe}")
        self.model_manager = ModelManager(
            config_manager=self.config_manager,
            error_handler=self.error_handler,
            terminal_id=self.terminal_id,
            timeframe=self.primary_timeframe # Use the determined primary timeframe
        )
        # Load models immediately for this context
        self.model_manager.load_all_models()

        # Initialize other trading components, passing necessary managers/configs
        self._init_trading_components()

        # Set up circuit breakers and system health monitors
        self._init_circuit_breakers()

        # Performance metrics
        self.metrics = {
            'terminal_id': self.terminal_id,
            'timeframe': self.primary_timeframe,
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_profit': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'last_update': datetime.now(),
            'processing_stats': None
        }

        # Register recovery handlers for critical errors
        self._register_recovery_handlers()

        logger.info(f"Trading bot initialization complete for Terminal {self.terminal_id}")

    def _get_primary_model_for_terminal(self) -> str:
        """Get the primary model type for this terminal based on configuration."""
        try:
            # Get terminal model pairings from config
            config = self.config_manager.get_config()
            if hasattr(config, 'terminal_model_pairings'):
                # Try to get from main config first
                terminal_pairings = getattr(config, 'terminal_model_pairings', {})
            else:
                # Fallback to loading from config file directly
                import json
                with open('config/config.json', 'r') as f:
                    config_data = json.load(f)
                terminal_pairings = config_data.get('terminal_model_pairings', {})

            # Get the primary model for this terminal
            terminal_config = terminal_pairings.get(str(self.terminal_id), {})
            primary_model = terminal_config.get('primary_model', 'lstm')  # Default to LSTM

            logger.info(f"Terminal {self.terminal_id} assigned primary model: {primary_model}")
            return primary_model

        except Exception as e:
            logger.warning(f"Could not determine primary model for terminal {self.terminal_id}: {str(e)}")
            logger.info(f"Using default model 'lstm' for terminal {self.terminal_id}")
            return 'lstm'  # Default fallback

    # Removed setup_logging - assume global setup in main.py
    # def setup_logging(self):
    #    ...

    def _register_recovery_handlers(self):
        """Register recovery handlers for critical errors"""
        # Example: MT5 connection issues (MT5Manager likely handles reconnects internally now)
        # self.error_handler.register_recovery_handler(
        #     ConnectionError, # Or specific MT5 error
        #     lambda ctx: self.mt5_manager.reconnect(ctx.get('terminal_id')) # Delegate to manager
        # )
        pass # Add specific handlers as needed

    # Removed _reconnect_mt5 - MT5Manager should handle this
    # def _reconnect_mt5(self, context: Dict[str, Any]) -> bool:
    #    ...

    def _handle_memory_error(self, context: Dict[str, Any]) -> bool:
        """Attempt to recover from a memory error."""
        logger.critical(f"Memory error detected: {context}. Attempting recovery...")
        try:
            # Use memory manager if available
            if self.memory_manager and hasattr(self.memory_manager, 'cleanup_memory'):
                logger.warning("Using memory manager for aggressive cleanup")
                self.memory_manager.cleanup_memory("AGGRESSIVE")
                logger.info("Memory manager cleanup completed")
                return True

            # Fallback to manual cleanup if memory manager not available
            logger.warning("Memory manager not available, performing manual cleanup")

            # Trigger garbage collection
            gc.collect()
            logger.warning("Forced garbage collection.")

            # Clear data caches (if DataProcessor is used and has a clear method)
            if hasattr(self, 'data_processor') and hasattr(self.data_processor, 'clear_cache'):
                self.data_processor.clear_cache()
                logger.warning("Cleared DataProcessor cache.")

            # Clear model caches
            if hasattr(self, 'model_manager') and hasattr(self.model_manager, 'get_all_models'):
                for model in self.model_manager.get_all_models().values():
                    if hasattr(model, 'clear_cache'):
                        try:
                            model.clear_cache()
                            logger.debug(f"Cleared cache for {model.model_name}")
                        except Exception as cache_err:
                            logger.error(f"Error clearing cache for {model.model_name}: {cache_err}")

            # Check memory usage again
            process = psutil.Process(os.getpid())
            mem_info = process.memory_info()
            logger.info(f"Memory usage after recovery attempt: {mem_info.rss / (1024 * 1024):.2f} MB")
            return True # Indicate recovery attempt made
        except Exception as e:
            logger.error(f"Recovery attempt from memory error failed during cleanup: {e}")
            return False # Recovery failed

    def _handle_model_error(self, context: Dict[str, Any]) -> bool:
        """Attempt to recover from a model loading or prediction error."""
        model_name = context.get('model_name', 'unknown')
        logger.error(f"Error related to model '{model_name}': {context.get('error')}. Attempting recovery...")
        try:
            # Mark the specific model as unhealthy
            if hasattr(self, 'model_manager') and model_name != 'unknown':
                self.model_manager.model_health[model_name] = False
                logger.warning(f"Marked model '{model_name}' as unhealthy.")

            # Attempt to reload the problematic model (optional, might retry the error)
            # if hasattr(self, 'model_manager') and model_name != 'unknown':
            #     logger.info(f"Attempting to reload model '{model_name}'...")
            #     reload_success = self.model_manager.reload_model(model_name)
            #     if reload_success:
            #         logger.info(f"Successfully reloaded model '{model_name}'.")
            #         return True # Recovered by reloading
            #     else:
            #         logger.error(f"Failed to reload model '{model_name}'.")

            # Strategy should ideally fall back to other healthy models
            logger.warning("Trading strategy should now attempt to use fallback models.")
            return True # Indicate recovery attempt (marking as unhealthy) was made
        except Exception as e:
            logger.error(f"Recovery from model error failed: {e}")
            return False

    # ... (Keep other _handle_* methods if relevant, update context/logging) ...

    # Removed _start_memory_monitoring - Handled by global MemoryManager in main.py
    # def _start_memory_monitoring(self):
    #    ...

    def _init_trading_components(self):
        """Initialize components like Strategy, Executor, Signal Generator"""
        logger.info("Initializing trading components...")
        try:
            # Signal Generator - needs access to models via ModelManager
            self.signal_generator = SignalGenerator(
                model_manager=self.model_manager, # Pass the context-specific manager
                config_manager=self.config_manager,
                error_handler=self.error_handler,
                terminal_id=self.terminal_id,
                timeframe=self.primary_timeframe
            )

            # Trading Strategy - needs SignalGenerator
            self.strategy = TradingStrategy(
                signal_generator=self.signal_generator,
                config=self.strategy_config # Pass StrategyConfig
            )

            # Trade Executor - needs MT5 connection via MT5Manager
            self.trade_executor = TradeExecutor(
                mt5_manager=self.mt5_manager,
                config=self.strategy_config, # Pass StrategyConfig
                error_handler=self.error_handler,
                terminal_id=self.terminal_id
            )
            logger.info("Trading components initialized successfully.")
        except Exception as e:
            self.error_handler.handle_error(
                exception=e,
                context={"method": "_init_trading_components", "terminal_id": self.terminal_id},
                source="TradingBot._init_trading_components",
                severity=ErrorSeverity.CRITICAL,
                category=ErrorCategory.SYSTEM
            )
            logger.critical(f"Failed to initialize critical trading components: {e}", exc_info=True)
            raise # Reraise critical initialization error

    def _init_circuit_breakers(self):
        """Initialize circuit breakers for critical operations."""
        logger.info("Initializing circuit breakers...")
        self.circuit_breakers = {
            'mt5_connection': CircuitBreaker(failure_threshold=3, recovery_timeout=60, name='MT5Connection'),
            'data_collection': CircuitBreaker(failure_threshold=5, recovery_timeout=120, name='DataCollection'),
            'prediction': CircuitBreaker(failure_threshold=3, recovery_timeout=300, name='ModelPrediction'),
            'trade_execution': CircuitBreaker(failure_threshold=2, recovery_timeout=60, name='TradeExecution')
        }
        logger.info("Circuit breakers initialized.")

    # ... (Keep _check_circuit_breakers, _update_performance_metrics) ...
    # ... Update methods below to use self.model_manager ...

    # Example update in _run_trading_cycle:
    def _run_trading_cycle(self) -> bool:
        """Runs a single trading cycle: data -> preprocess -> predict -> decide -> execute."""
        logger.info(f"[{self.terminal_id}] *** STARTING TRADING CYCLE for {self.primary_timeframe} ***")
        market_data: Optional[pd.DataFrame] = None # Initialize
        try:
            # 1. Data Collection with minimal MT5 connection
            logger.debug(f"[{self.terminal_id}] Collecting market data with minimal MT5 connection...")

            # Establish minimal MT5 connection for data collection
            from utils.mt5.mt5_initializer import MT5Initializer
            if not MT5Initializer.minimal_init():
                logger.error(f"[{self.terminal_id}] Failed to establish minimal MT5 connection")
                return False

            # Collect market data (simplified for now)
            import MetaTrader5 as mt5

            # Get current symbol data
            symbol = self.config.strategy.symbol
            timeframe = getattr(mt5, f'TIMEFRAME_{self.primary_timeframe}', mt5.TIMEFRAME_M5)

            # Get recent bars
            bars = mt5.copy_rates_from_pos(symbol, timeframe, 0, 100)
            if bars is None or len(bars) == 0:
                logger.warning(f"[{self.terminal_id}] No market data available for {symbol}")
                return False

            # Convert to DataFrame
            import pandas as pd
            market_data = pd.DataFrame(bars)
            market_data['time'] = pd.to_datetime(market_data['time'], unit='s')

            # Fix volume column naming - MT5 returns 'tick_volume' and 'real_volume', but we need 'volume'
            if 'volume' not in market_data.columns:
                if 'real_volume' in market_data.columns:
                    market_data['volume'] = market_data['real_volume']
                elif 'tick_volume' in market_data.columns:
                    market_data['volume'] = market_data['tick_volume']
                else:
                    logger.warning(f"[{self.terminal_id}] No volume data available, using zeros")
                    market_data['volume'] = 0

            logger.info(f"[{self.terminal_id}] Collected {len(market_data)} bars for {symbol}")
            logger.debug(f"[{self.terminal_id}] Market data columns: {list(market_data.columns)}")

            # 2. Preprocess data
            processed_data = market_data  # Default fallback
            sequences = None
            if self.data_preprocessor:
                try:
                    # Use the correct method name and get required feature columns
                    feature_cols = ['open', 'high', 'low', 'close', 'volume']
                    processed_data, sequences = self.data_preprocessor.preprocess_data(market_data, feature_cols)
                    if processed_data is not None and sequences is not None:
                        logger.debug(f"[{self.terminal_id}] Data preprocessing completed. Sequences shape: {sequences.shape}")
                    else:
                        logger.warning(f"[{self.terminal_id}] Data preprocessing returned None")
                        processed_data = market_data
                        sequences = None
                except Exception as e:
                    logger.error(f"[{self.terminal_id}] Data preprocessing failed: {str(e)}")
                    processed_data = market_data  # Fallback to raw data
                    sequences = None
            else:
                processed_data = market_data

            # 3. Generate predictions using models
            predictions = {}
            if self.model_manager and sequences is not None:
                try:
                    for model_name in ['lstm', 'tft', 'arima']:
                        model = self.model_manager.get_model(model_name)
                        if model:
                            # Use sequences for LSTM and TFT, processed_data for ARIMA
                            if model_name in ['lstm', 'tft']:
                                # Use the last sequence for prediction
                                model_input = sequences[-1:] if len(sequences) > 0 else sequences
                            else:  # ARIMA
                                model_input = processed_data

                            prediction = model.predict(model_input)
                            predictions[model_name] = prediction
                            logger.debug(f"[{self.terminal_id}] {model_name} prediction: {prediction}")
                except Exception as e:
                    logger.error(f"[{self.terminal_id}] Model prediction failed: {str(e)}")
                    return False
            elif sequences is None:
                logger.warning(f"[{self.terminal_id}] No sequences available for model prediction")
                return False

            # 4. Generate trading signals
            if self.signal_generator and sequences is not None:
                try:
                    # Get the most recent data point for current price/volume info
                    current_data_point = processed_data.iloc[-1] if processed_data is not None and len(processed_data) > 0 else None
                    signal = self.signal_generator.generate_signal(sequences, current_data_point)
                    if signal:
                        logger.info(f"[{self.terminal_id}] Generated trading signal: {signal.action} with confidence {signal.confidence:.3f}")

                        # Log signal to comprehensive logging system
                        try:
                            signal_data = {
                                'direction': signal.action,
                                'confidence': signal.confidence,
                                'symbol': self.strategy_config.symbol,
                                'models_used': getattr(signal, 'models_used', ['lstm', 'tft', 'arima']),
                                'timestamp': datetime.now().isoformat()
                            }
                            log_signal(self.terminal_id, signal_data)

                            # Update terminal activity
                            terminal_manager = get_terminal_manager()
                            terminal_manager.update_terminal_activity(self.terminal_id, "signal")
                        except Exception as e:
                            logger.warning(f"[{self.terminal_id}] Failed to log signal: {str(e)}")
                            # Continue without logging

                        signals = [signal]  # Convert single signal to list for compatibility
                    else:
                        logger.debug(f"[{self.terminal_id}] No trading signal generated")
                        signals = []
                except Exception as e:
                    logger.error(f"[{self.terminal_id}] Signal generation failed: {str(e)}")
                    try:
                        log_terminal_error(self.terminal_id, f"Signal generation failed: {str(e)}", "SIGNAL_ERROR")
                    except:
                        pass  # Don't let logging errors break the bot
                    return False
            else:
                signals = []

            # 5. Apply strategy and execute trades
            if hasattr(self, 'strategy') and hasattr(self, 'trade_executor') and self.strategy and self.trade_executor and signals:
                try:
                    for signal in signals:
                        # Apply strategy rules
                        trade_decision = self.strategy.decide_trade(signal)

                        if trade_decision:
                            # Execute actual trade
                            logger.info(f"[{self.terminal_id}] Executing trade: {trade_decision.action} {symbol}")

                            # Execute the actual trade
                            trade_result = self.trade_executor.execute_trade(trade_decision)

                            # Log trade execution result using comprehensive logging
                            if trade_result.get('success', False):
                                logger.info(f"[{self.terminal_id}] Trade executed successfully: {trade_result}")

                                # Log to comprehensive logging system
                                try:
                                    trade_data = {
                                        'action': trade_decision.action,
                                        'symbol': symbol,
                                        'volume': trade_result.get('volume', 0.0),
                                        'price': trade_result.get('price', 0.0),
                                        'profit_loss': trade_result.get('profit_loss', 0.0),
                                        'ticket': trade_result.get('ticket', 'unknown'),
                                        'timestamp': datetime.now().isoformat()
                                    }
                                    log_trade(self.terminal_id, trade_data)

                                    # Update terminal activity
                                    terminal_manager = get_terminal_manager()
                                    terminal_manager.update_terminal_activity(self.terminal_id, "trade")
                                except Exception as e:
                                    logger.warning(f"[{self.terminal_id}] Failed to log trade: {str(e)}")
                                    # Continue without logging

                            else:
                                logger.warning(f"[{self.terminal_id}] Trade execution failed: {trade_result}")
                                try:
                                    log_terminal_warning(self.terminal_id, f"Trade execution failed: {trade_result.get('message', 'Unknown error')}")
                                except:
                                    pass  # Don't let logging errors break the bot

                                # Continue with simulated result for metrics if trade fails
                                trade_result = {
                                    'symbol': symbol,
                                    'action': trade_decision.action,
                                    'profit': 0.0,  # Failed trade
                                    'status': 'failed'
                                }

                            # Update performance metrics
                            self._update_performance_metrics(trade_result)

                            # Update trade profits for all active positions
                            if hasattr(self, 'trade_executor') and hasattr(self.trade_executor, 'update_trade_profits'):
                                self.trade_executor.update_trade_profits()

                except Exception as e:
                    logger.error(f"[{self.terminal_id}] Trade execution failed: {str(e)}")
                    return False

            logger.info(f"[{self.terminal_id}] Trading cycle completed successfully")
            return True

        except CircuitOpenError as coe:
             logger.warning(f"[{self.terminal_id}] Circuit breaker '{coe.breaker_name}' is open. Skipping trading cycle. Reason: {coe}")
             return False # Cycle skipped due to open circuit breaker
        except Exception as e:
             logger.error(f"[{self.terminal_id}] Unhandled error in trading cycle: {e}", exc_info=True)
             self.error_handler.handle_error(
                 exception=e,
                 context={"method": "_run_trading_cycle", "terminal_id": self.terminal_id},
                 source="TradingBot._run_trading_cycle",
                 severity=ErrorSeverity.ERROR,
                 category=ErrorCategory.EXECUTION
             )
             # Increment error counts or trigger recovery?
             return False # Cycle failed

    # ... (Keep run method) ...

    def update(self):
        """Update the trading bot state. Called periodically by the manager."""
        try:
            # Check if the bot is running
            if not hasattr(self, 'running_thread') or not self.running_thread.is_alive():
                logger.warning(f"[{self.terminal_id}] Bot thread is not running")
                return

            # Update performance metrics if needed
            if hasattr(self, 'metrics'):
                self.performance_metrics = self.metrics

            # Check system health
            self._check_system_requirements()

            # Log current state
            logger.debug(f"[{self.terminal_id}] Bot updated. Status: Running")
        except Exception as e:
            logger.error(f"[{self.terminal_id}] Error updating bot: {str(e)}")
            if hasattr(self, 'error_handler'):
                self.error_handler.handle_error(
                    exception=e,
                    context={"method": "update", "terminal_id": self.terminal_id},
                    source="TradingBot.update",
                    severity=ErrorSeverity.WARNING,
                    category=ErrorCategory.SYSTEM
                )

    def stop(self):
        """Stop the trading bot with proper cleanup"""
        logger.info(f"[{self.terminal_id}] Stopping trading bot...")

        # Set stop event
        self.stop_event.set()

        # Wait for thread to finish
        if self.running_thread and self.running_thread.is_alive():
            logger.debug(f"[{self.terminal_id}] Waiting for trading loop thread to join...")
            self.running_thread.join(timeout=30)
            if self.running_thread.is_alive():
                 logger.warning(f"[{self.terminal_id}] Trading loop thread did not exit cleanly after 30s.")

        # Clean up resources
        logger.debug(f"[{self.terminal_id}] Cleaning up bot resources...")
        try:
            # Shutdown MT5 connection for this bot's perspective (Manager handles actual disconnect)
            # self.mt5_manager.shutdown(self.terminal_id) # Optional: signal manager this bot is done

            # Clear model caches (Iterate through models managed by THIS instance)
            logger.debug(f"[{self.terminal_id}] Clearing model caches...")
            # for model in self.model_manager.models.values(): # Accessing dict directly might be unsafe
            for model in self.model_manager.get_all_models().values(): # Use getter
                if hasattr(model, 'clear_cache'):
                    try:
                         model.clear_cache()
                         logger.debug(f"[{self.terminal_id}] Cleared cache for {model.model_name}")
                    except Exception as cache_err:
                         logger.error(f"[{self.terminal_id}] Error clearing cache for {model.model_name}: {cache_err}")

            # Force garbage collection
            gc.collect()

            logger.info(f"[{self.terminal_id}] Trading bot stopped and resources cleaned up.")

        except Exception as e:
            logger.error(f"[{self.terminal_id}] Error during cleanup: {e}", exc_info=True)
            self.error_handler.handle_error(e, context={"method": "stop", "terminal_id": self.terminal_id})

    def _check_system_requirements(self) -> bool:
        """
        Check if system requirements are met

        Returns:
            True if requirements are met, False otherwise
        """
        try:
            # Check memory using memory manager if available
            if self.memory_manager and hasattr(self.memory_manager, 'is_memory_critical'):
                if self.memory_manager.is_memory_critical():
                    logger.warning(f"[{self.terminal_id}] Memory usage is critical according to memory manager")
                    return False
            else:
                # Fallback to direct check if memory manager not available
                process = psutil.Process(os.getpid())
                mem_info = process.memory_info()
                memory_usage_mb = mem_info.rss / (1024 * 1024)

                # Check if max_memory_usage is a percentage or absolute value
                max_memory = self.config.max_memory_usage # From TradingConfig

                # If max_memory is less than 100, treat it as a percentage
                if max_memory < 100:
                    # Convert percentage to MB
                    total_memory_mb = psutil.virtual_memory().total / (1024 * 1024)
                    max_memory_mb = (max_memory / 100) * total_memory_mb
                    logger.debug(f"[{self.terminal_id}] Max memory: {max_memory}% = {max_memory_mb:.2f} MB")
                else:
                    # Already in MB
                    max_memory_mb = max_memory
                    logger.debug(f"[{self.terminal_id}] Max memory: {max_memory_mb:.2f} MB")

                # Calculate memory buffer (5% of total memory or 1GB, whichever is smaller)
                total_memory_mb = psutil.virtual_memory().total / (1024 * 1024)
                memory_buffer_mb = min(total_memory_mb * 0.05, 1024)

                # Adjust max_memory_mb to account for buffer
                adjusted_max_memory_mb = max_memory_mb - memory_buffer_mb

                if memory_usage_mb > adjusted_max_memory_mb:
                    # Only log a warning if we're within the buffer zone
                    if memory_usage_mb <= max_memory_mb:
                        logger.warning(f"[{self.terminal_id}] Memory usage ({memory_usage_mb:.2f} MB) approaching threshold ({max_memory_mb:.2f} MB)")
                        # Continue execution but trigger garbage collection
                        gc.collect()
                        return True
                    else:
                        logger.warning(f"[{self.terminal_id}] Memory usage ({memory_usage_mb:.2f} MB) exceeds threshold ({max_memory_mb:.2f} MB)")
                        # Try to free memory
                        gc.collect()
                        # Check if garbage collection helped
                        new_memory_usage_mb = process.memory_info().rss / (1024 * 1024)
                        if new_memory_usage_mb <= adjusted_max_memory_mb:
                            logger.info(f"[{self.terminal_id}] Memory usage reduced to {new_memory_usage_mb:.2f} MB after garbage collection")
                            return True
                        return False

            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.5) # Shorter interval might be noisy
            max_cpu = 90.0 # Make this configurable?
            if cpu_percent > max_cpu:
                logger.warning(f"[{self.terminal_id}] CPU usage ({cpu_percent}%) exceeds threshold ({max_cpu}%)")
                return False

            # Check disk space (Check on path where data/models are stored)
            # Use data_base_path as a representative location
            check_path = Path(self.config_manager.get_data_base_path()).anchor or '/' # Get drive or root
            disk = psutil.disk_usage(check_path)
            min_disk_mb = 1024 # Make this configurable?
            if disk.free / (1024 * 1024) < min_disk_mb:
                logger.warning(f"[{self.terminal_id}] Disk space on {check_path} ({disk.free / (1024 * 1024):.2f} MB) below threshold ({min_disk_mb} MB)")
                return False

            # Check MT5 connection with minimal initialization to preserve algorithmic trading
            logger.debug(f"[{self.terminal_id}] Checking MT5 connection with minimal initialization...")

            # Try to establish minimal MT5 connection
            if self.mt5_manager:
                try:
                    # Use minimal MT5 initialization to preserve algorithmic trading
                    from utils.mt5.mt5_initializer import MT5Initializer
                    if MT5Initializer.minimal_init():
                        logger.debug(f"[{self.terminal_id}] MT5 minimal connection established")
                    else:
                        logger.warning(f"[{self.terminal_id}] MT5 minimal connection failed, will retry when needed")
                except Exception as e:
                    logger.warning(f"[{self.terminal_id}] MT5 connection check failed: {str(e)}")
            else:
                logger.warning(f"[{self.terminal_id}] MT5 manager not available")

            return True

        except Exception as e:
            logger.error(f"[{self.terminal_id}] Error checking system requirements: {e}", exc_info=True)
            self.error_handler.handle_error(
                exception=e,
                context={"method": "_check_system_requirements", "terminal_id": self.terminal_id},
                source="TradingBot._check_system_requirements",
                severity=ErrorSeverity.WARNING,
                category=ErrorCategory.SYSTEM
            )
            return False

    # Removed _handle_trading_cycle_error method as we're now using EnhancedErrorHandler's with_error_handling

    # Keep _check_circuit_breakers, _update_performance_metrics as they seem self-contained for now
    # Although _update_performance_metrics might need adjustment based on trade_result structure



    def _check_circuit_breakers(self) -> bool:
        """Check circuit breakers for critical operations."""
        for breaker_name, breaker in self.circuit_breakers.items():
            if breaker.state == CircuitState.OPEN:
                logger.warning(f"[{self.terminal_id}] Circuit breaker '{breaker_name}' is open. Skipping operation.")
                return False
        return True

    def _update_performance_metrics(self, trade_result):
        """Update performance metrics based on trade result."""
        self.metrics['total_trades'] += 1

        # Handle both dictionary and object types
        if isinstance(trade_result, dict):
            profit = trade_result.get('profit', 0.0)  # Get profit from dict
        else:
            # Handle object type (like TradingSignal or TradeOrder)
            # Initialize profit to 0.0 as a safe default
            profit = 0.0

            # Try to get profit from various attributes
            try:
                if hasattr(trade_result, 'profit'):
                    profit = trade_result.profit
                elif hasattr(trade_result, 'profit_loss'):
                    profit = trade_result.profit_loss

                # Ensure profit and profit_loss attributes exist
                if not hasattr(trade_result, 'profit'):
                    trade_result.profit = profit
                if not hasattr(trade_result, 'profit_loss'):
                    trade_result.profit_loss = profit
            except Exception as e:
                logger.warning(f"Could not access or set profit attributes on trade_result: {e}")

        if profit > 0:
            self.metrics['winning_trades'] += 1
        else:
            self.metrics['losing_trades'] += 1

        self.metrics['total_profit'] += profit  # Use actual profit
        self.metrics['last_update'] = datetime.now()

        # Get processing stats if available
        if hasattr(self.data_preprocessor, 'get_stats'):
            self.metrics['processing_stats'] = self.data_preprocessor.get_stats()

        # Calculate win rate safely
        if self.metrics['total_trades'] > 0:
            self.metrics['win_rate'] = (self.metrics['winning_trades'] / self.metrics['total_trades']) * 100
        else:
            self.metrics['win_rate'] = 0.0

        # Calculate max drawdown if method exists
        self.metrics['max_drawdown'] = self._calculate_max_drawdown() if hasattr(self, '_calculate_max_drawdown') else 0.0

        logger.info(f"[{self.terminal_id}] Performance Updated: WinRate={self.metrics['win_rate']:.2f}%, Profit={self.metrics['total_profit']:.2f}")

    def _calculate_max_drawdown(self) -> float:
        """Calculate the maximum drawdown based on trade results."""
        # Ensure trade_executor and trade_history exist and are populated correctly
        if not hasattr(self, 'trade_executor') or not hasattr(self.trade_executor, 'trade_history') or not self.trade_executor.trade_history:
            return 0.0

        # Starting equity is 0 or could be fetched from account balance
        current_equity = 0.0
        peak_equity = 0.0
        max_drawdown = 0.0

        for trade in self.trade_executor.trade_history:
            # Handle both dictionary and object types
            if isinstance(trade, dict):
                profit = trade.get('profit', 0.0)
            else:
                # Handle object type (like TradingSignal or TradeOrder)
                # Initialize profit to 0.0 as a safe default
                profit = 0.0

                # Try to get profit from various attributes
                try:
                    if hasattr(trade, 'profit'):
                        profit = trade.profit
                    elif hasattr(trade, 'profit_loss'):
                        profit = trade.profit_loss

                    # Ensure profit and profit_loss attributes exist
                    if not hasattr(trade, 'profit'):
                        trade.profit = profit
                    if not hasattr(trade, 'profit_loss'):
                        trade.profit_loss = profit
                except Exception as e:
                    logger.warning(f"Could not access or set profit attributes on trade: {e}")

            if profit is None:
                continue

            current_equity += profit
            peak_equity = max(peak_equity, current_equity)
            drawdown = peak_equity - current_equity  # Absolute drawdown
            # drawdown_percent = (drawdown / peak_equity) * 100 if peak_equity > 0 else 0
            max_drawdown = max(max_drawdown, drawdown)

        return max_drawdown # Return absolute drawdown for consistency with metrics example

    # ... (Keep run, stop, _check_system_requirements, _handle_trading_cycle_error) ...
    # ... Ensure terminal_id is used correctly in logging and context ...

    def start(self):
        """Start the trading bot in a separate thread."""
        logger.info(f"[{self.terminal_id}] Starting trading bot...")

        if self.running_thread is not None and self.running_thread.is_alive():
            logger.warning(f"[{self.terminal_id}] Trading bot is already running")
            return True

        # Start the trading bot in a separate thread
        self.running_thread = threading.Thread(
            target=self.run,
            name=f"TradingBot-{self.terminal_id}",
            daemon=True
        )
        self.running_thread.start()

        logger.info(f"[{self.terminal_id}] Trading bot started in thread: {self.running_thread.name}")
        return True

    def is_running(self):
        """Check if the trading bot is currently running."""
        return (self.running_thread is not None and
                self.running_thread.is_alive() and
                not self.stop_event.is_set())

    def stop(self):
        """Stop the trading bot."""
        logger.info(f"[{self.terminal_id}] Stopping trading bot...")

        # Set the stop event
        self.stop_event.set()

        # Wait for the thread to finish
        if self.running_thread is not None and self.running_thread.is_alive():
            logger.info(f"[{self.terminal_id}] Waiting for trading bot thread to finish...")
            self.running_thread.join(timeout=10)  # Wait up to 10 seconds

            if self.running_thread.is_alive():
                logger.warning(f"[{self.terminal_id}] Trading bot thread did not stop gracefully")
            else:
                logger.info(f"[{self.terminal_id}] Trading bot stopped successfully")

        return True

    def run(self, interval_seconds: int = 300):
        """
        Run the trading bot in a loop with improved error handling

        Args:
            interval_seconds: Interval between trading cycles in seconds (default: 300 = 5 minutes)
        """
        # Initialize error count if not already present
        if not hasattr(self, 'error_count'):
            self.error_count = 0

        def _trading_loop():
            logger.info(f"[{self.terminal_id}] Trading loop started")
            while not self.stop_event.is_set():
                try:
                    logger.info(f"[{self.terminal_id}] Starting new trading cycle iteration")

                    # Update trade profits regularly
                    if hasattr(self, 'trade_executor') and hasattr(self.trade_executor, 'update_trade_profits'):
                        try:
                            self.trade_executor.update_trade_profits()
                        except Exception as e:
                            logger.warning(f"[{self.terminal_id}] Error updating trade profits: {str(e)}")
                    elif not hasattr(self, 'trade_executor'):
                        logger.warning(f"[{self.terminal_id}] Trade executor not initialized, skipping profit update")

                    # Check circuit breakers before proceeding
                    logger.debug(f"[{self.terminal_id}] Checking circuit breakers...")
                    if not self._check_circuit_breakers():
                        logger.warning(f"[{self.terminal_id}] Circuit breaker triggered. Waiting for reset...")
                        time.sleep(interval_seconds)
                        continue

                    # Check system requirements
                    logger.debug(f"[{self.terminal_id}] Checking system requirements...")
                    if not self._check_system_requirements():
                        logger.error(f"[{self.terminal_id}] System requirements not met. Waiting...")
                        time.sleep(interval_seconds)
                        continue

                    logger.info(f"[{self.terminal_id}] All checks passed, executing trading cycle...")

                    # Run a single trading cycle with error handling
                    try:
                        success = self._run_trading_cycle()
                        logger.info(f"[{self.terminal_id}] Trading cycle completed with success: {success}")

                        if not success:
                            self.error_count += 1
                            logger.error(f"[{self.terminal_id}] Trading cycle failed. Error count: {self.error_count}")

                            # Log error to comprehensive logging
                            try:
                                log_terminal_error(self.terminal_id, f"Trading cycle failed (error count: {self.error_count})", "TRADING_CYCLE_ERROR")
                            except:
                                pass  # Don't let logging errors break the bot
                        else:
                            # Reset error count if successful
                            self.error_count = 0

                            # Update terminal activity
                            try:
                                terminal_manager = get_terminal_manager()
                                terminal_manager.update_terminal_activity(self.terminal_id, "general")
                            except:
                                pass  # Don't let logging errors break the bot

                            # CRITICAL FIX: Log performance metrics after successful cycle
                            if hasattr(self, 'trade_executor') and hasattr(self.trade_executor, 'get_performance_metrics'):
                                try:
                                    metrics = self.trade_executor.get_performance_metrics()
                                    if metrics.get('total_trades', 0) > 0:
                                        logger.info(f"[{self.terminal_id}] Performance: {metrics['total_trades']} trades, "
                                                  f"{metrics.get('win_rate', 0):.1f}% win rate, "
                                                  f"${metrics.get('total_profit', 0):.2f} total P&L, "
                                                  f"{metrics.get('active_positions', 0)} active positions")
                                except Exception as e:
                                    logger.debug(f"[{self.terminal_id}] Error logging performance metrics: {str(e)}")

                    except Exception as e:
                        logger.error(f"[{self.terminal_id}] Exception in trading cycle: {str(e)}", exc_info=True)
                        self.error_count += 1
                        success = False

                        # Log error to comprehensive logging
                        try:
                            log_terminal_error(self.terminal_id, f"Trading cycle exception: {str(e)}", "TRADING_CYCLE_EXCEPTION")
                        except:
                            pass

                    # Wait for the next interval
                    logger.info(f"[{self.terminal_id}] Waiting {interval_seconds} seconds until next trading cycle")
                    for _ in range(interval_seconds):
                        if self.stop_event.is_set():
                            break
                        time.sleep(1)

                except Exception as e:
                    logger.error(f"[{self.terminal_id}] Error in trading loop: {str(e)}", exc_info=True)
                    self.error_count += 1
                    time.sleep(interval_seconds)

            logger.info(f"[{self.terminal_id}] Trading loop ended")

        # Start the trading loop in a separate thread
        self.running_thread = threading.Thread(target=_trading_loop, daemon=True)
        self.running_thread.start()
        logger.info(f"[{self.terminal_id}] Trading bot started")