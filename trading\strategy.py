"""
Trading strategy module for generating trading signals based on model predictions.
"""
import logging
from typing import Dict, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class TradingStrategy:
    def __init__(self, config: Dict, signal_generator=None):
        """
        Initialize the trading strategy.

        Args:
            config: Strategy configuration
            signal_generator: Optional signal generator instance
        """
        self.config = config
        self.signal_generator = signal_generator
        self.trade_history = []  # Initialize trade history
        self.model_weights = {
            'lstm': 0.35,
            'tft': 0.35,
            'arima': 0.30
        }
        # Check if config is a dictionary or an object
        if isinstance(config, dict):
            # If it's a dictionary, use get method
            self.min_confidence = config.get('min_confidence', 0.6)  # Reduced as requested
            self.max_spread = config.get('max_spread', 5000)  # Adjusted for BTCUSD.a crypto volatility
            self.stop_loss = config.get('stop_loss', 200)  # Adjusted for BTCUSD.a volatility
            self.take_profit = config.get('take_profit', 400)  # Adjusted for BTCUSD.a volatility

            # BTCUSD.a specific parameters
            self.volatility_threshold = config.get('volatility_threshold', 2.0)
            self.trend_threshold = config.get('trend_threshold', 0.7)
            self.position_sizing_factor = config.get('position_sizing_factor', 0.5)

            # Risk management
            self.max_daily_loss = config.get('max_daily_loss', 50.0)
            self.max_daily_trades = config.get('max_daily_trades', 5)
            self.cooldown_period = config.get('cooldown_period', 600)
        else:
            # If it's an object, use getattr
            self.min_confidence = getattr(config, 'min_confidence', 0.6)  # Reduced as requested
            self.max_spread = getattr(config, 'max_spread', 5000)  # Adjusted for BTCUSD.a crypto volatility
            self.stop_loss = getattr(config, 'stop_loss', 200)  # Adjusted for BTCUSD.a volatility
            self.take_profit = getattr(config, 'take_profit', 400)  # Adjusted for BTCUSD.a volatility

            # BTCUSD.a specific parameters
            self.volatility_threshold = getattr(config, 'volatility_threshold', 2.0)
            self.trend_threshold = getattr(config, 'trend_threshold', 0.7)
            self.position_sizing_factor = getattr(config, 'position_sizing_factor', 0.5)

            # Risk management
            self.max_daily_loss = getattr(config, 'max_daily_loss', 50.0)
            self.max_daily_trades = getattr(config, 'max_daily_trades', 5)
            self.cooldown_period = getattr(config, 'cooldown_period', 600)

        # Fixed parameters
        self.volatility_lookback = 20  # Increased for crypto
        self.trend_lookback = 50  # Increased for crypto

        logger.info("Trading strategy initialized for BTCUSD.a with adjusted parameters")

    def update_model_weights(self, predictions: Dict[str, float], metrics: Dict) -> None:
        """
        Update model weights based on performance metrics.

        Args:
            predictions: Dictionary of model predictions
            metrics: Dictionary of performance metrics
        """
        try:
            if not predictions or not metrics:
                logger.warning("Invalid predictions or metrics for weight update")
                return

            # Calculate performance scores for each model
            performance_scores = {}
            total_score = 0.0

            for model_name, pred in predictions.items():
                if model_name not in self.model_weights:
                    continue

                # Calculate score based on prediction accuracy and metrics
                score = 0.0

                # Add points for correct predictions
                if metrics['total_trades'] > 0:
                    win_rate = metrics['winning_trades'] / metrics['total_trades']
                    if (pred > 0 and win_rate > 0.5) or (pred < 0 and win_rate < 0.5):
                        score += 1.0

                # Add points for profit contribution
                if metrics['total_profit'] != 0:
                    profit_contribution = abs(pred) * metrics['total_profit']
                    score += profit_contribution / abs(metrics['total_profit'])

                # Add points for risk-adjusted performance
                if metrics['sharpe_ratio'] > 0:
                    score += metrics['sharpe_ratio'] * 0.5

                performance_scores[model_name] = max(0.1, score)  # Ensure minimum weight
                total_score += performance_scores[model_name]

            if total_score > 0:
                # Normalize weights
                for model_name in self.model_weights:
                    if model_name in performance_scores:
                        self.model_weights[model_name] = performance_scores[model_name] / total_score
                    else:
                        self.model_weights[model_name] = 0.0

                logger.info(f"Updated model weights: {self.model_weights}")

        except Exception as e:
            logger.error(f"Error updating model weights: {str(e)}")

    def generate_trading_signal(self, predictions: Dict[str, float], metrics: Dict) -> Optional[Dict]:
        """
        Generate trading signal based on model predictions and metrics.

        Args:
            predictions: Dictionary of model predictions
            metrics: Dictionary of performance metrics

        Returns:
            Optional[Dict]: Trading signal or None if no valid signal
        """
        try:
            if not predictions or not metrics:
                logger.error("Invalid predictions or metrics for signal generation")
                return None

            # Calculate weighted prediction
            weighted_pred = 0.0
            total_weight = 0.0

            for model_name, pred in predictions.items():
                if model_name in self.model_weights:
                    weight = self.model_weights[model_name]
                    weighted_pred += pred * weight
                    total_weight += weight

            if total_weight == 0:
                logger.error("No valid model weights for signal generation")
                return None

            weighted_pred /= total_weight

            # Calculate confidence level
            confidence = abs(weighted_pred)

            # Check if confidence meets minimum threshold
            if confidence < self.min_confidence:
                logger.info(f"Confidence {confidence} below minimum threshold {self.min_confidence}")
                return {'action': 'hold', 'confidence': confidence}

            # Generate signal
            if weighted_pred > 0:
                action = 'buy'
            else:
                action = 'sell'

            # Calculate position size based on confidence and metrics
            position_size = self._calculate_position_size(confidence, metrics)

            return {
                'action': action,
                'confidence': confidence,
                'position_size': position_size,
                'stop_loss': self.stop_loss,
                'take_profit': self.take_profit
            }

        except Exception as e:
            logger.error(f"Error generating trading signal: {str(e)}")
            return None

    def _calculate_position_size(self, confidence: float, metrics: Dict) -> float:
        """
        Calculate position size based on confidence and metrics.
        Adjusted for BTCUSD.a volatility and risk.

        Args:
            confidence: Signal confidence
            metrics: Performance metrics

        Returns:
            float: Position size
        """
        try:
            # Base position size (more conservative for BTCUSD.a)
            if isinstance(self.config, dict):
                base_size = self.config.get('lot_size', 0.01)
            else:
                base_size = getattr(self.config, 'lot_size', 0.01)

            # Adjust based on confidence
            confidence_multiplier = min(confidence / self.min_confidence, 1.5)

            # Adjust based on volatility
            volatility_multiplier = 1.0
            if isinstance(metrics, dict) and metrics.get('volatility', 0) > self.volatility_threshold:
                volatility_multiplier = 0.5  # Reduce size in high volatility

            # Adjust based on performance
            performance_multiplier = 1.0
            if isinstance(metrics, dict) and metrics.get('total_trades', 0) > 0:
                win_rate = metrics.get('winning_trades', 0) / metrics.get('total_trades', 1)
                if win_rate > 0.6:
                    performance_multiplier = 1.1
                elif win_rate < 0.4:
                    performance_multiplier = 0.7

            # Calculate final position size
            position_size = (
                base_size *
                confidence_multiplier *
                volatility_multiplier *
                performance_multiplier *
                self.position_sizing_factor
            )

            # Ensure position size is within BTCUSD.a limits
            min_size = 0.01  # Minimum lot size for BTCUSD.a
            max_size = 0.5   # Maximum lot size for BTCUSD.a

            return max(min_size, min(position_size, max_size))

        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            return 0.01

    def calculate_position_size(
        self,
        signal: Dict,
        account_balance: float,
        risk_per_trade: float = 0.02
    ) -> float:
        """
        Calculate position size based on signal confidence and risk parameters.

        Args:
            signal: Trading signal
            account_balance: Current account balance
            risk_per_trade: Risk per trade as fraction of account balance

        Returns:
            float: Position size in lots
        """
        try:
            if signal['action'] == 'hold':
                return 0.0

            # Calculate base position size
            risk_amount = account_balance * risk_per_trade

            # Get lot size based on config type
            if isinstance(self.config, dict):
                lot_size = self.config.get('LOT_SIZE', 0.1)
            else:
                lot_size = getattr(self.config, 'LOT_SIZE', 0.1)

            # Adjust position size based on signal confidence
            adjusted_size = lot_size * signal['confidence']

            # Ensure position size doesn't exceed maximum allowed
            max_size = min(
                lot_size,
                risk_amount / (signal['price'] * 100)  # Convert risk amount to lots
            )

            return min(adjusted_size, max_size)

        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            raise

    def calculate_stop_loss(self, signal: Dict, current_price: float, volatility: float) -> float:
        """
        Calculate dynamic stop-loss level for BTCUSD.a.

        Args:
            signal: Trading signal
            current_price: Current market price
            volatility: Current market volatility

        Returns:
            float: Stop-loss price
        """
        try:
            # Base stop-loss distance based on volatility
            base_distance = volatility * 2.5  # Increased for crypto

            # Adjust distance based on signal confidence
            adjusted_distance = base_distance * (1 - signal['confidence'])

            # Add additional buffer for BTCUSD.a
            buffer = volatility * 0.5

            if signal['action'] == 'buy':
                stop_loss = current_price * (1 - (adjusted_distance + buffer))
            elif signal['action'] == 'sell':
                stop_loss = current_price * (1 + (adjusted_distance + buffer))
            else:
                stop_loss = current_price

            return stop_loss

        except Exception as e:
            logger.error(f"Error calculating stop-loss: {str(e)}")
            raise

    def calculate_take_profit(
        self,
        signal: Dict,
        current_price: float,
        stop_loss: float,
        volatility: float = 0.0
    ) -> float:
        """
        Calculate dynamic take-profit level.

        Args:
            signal: Trading signal
            current_price: Current market price
            stop_loss: Stop-loss price
            volatility: Current market volatility (optional)

        Returns:
            float: Take-profit price
        """
        try:
            # Calculate risk-reward ratio based on volatility
            risk_reward_ratio = 2.0  # Base ratio

            # Adjust ratio based on volatility if provided
            if volatility > 0:
                # Higher volatility = higher potential reward
                volatility_factor = min(volatility / 2.0, 1.5)
                risk_reward_ratio *= volatility_factor

            # Adjust ratio based on signal confidence
            if isinstance(signal, dict) and 'confidence' in signal:
                adjusted_ratio = risk_reward_ratio * (1 + signal['confidence'])
            else:
                adjusted_ratio = risk_reward_ratio

            if isinstance(signal, dict) and signal.get('action') == 'buy':
                risk = current_price - stop_loss
                take_profit = current_price + (risk * adjusted_ratio)
            elif isinstance(signal, dict) and signal.get('action') == 'sell':
                risk = stop_loss - current_price
                take_profit = current_price - (risk * adjusted_ratio)
            else:
                take_profit = current_price

            return take_profit

        except Exception as e:
            logger.error(f"Error calculating take-profit: {str(e)}")
            raise

    def decide_trade(self, signal) -> Optional[Any]:
        """
        Decide whether to execute a trade based on the signal.

        Args:
            signal: Trading signal object

        Returns:
            Optional[Any]: Trade order or None if no trade should be executed
        """
        try:
            if not signal or not hasattr(signal, 'action') or signal.action == 'hold':
                logger.info("No trade: Signal is None or hold action")
                return None

            # Check confidence threshold
            if signal.confidence < self.min_confidence:
                logger.info(f"No trade: Confidence {signal.confidence:.4f} below threshold {self.min_confidence}")
                return None

            # Return the signal as the trade order
            # The signal already has all the necessary attributes
            return signal

        except Exception as e:
            logger.error(f"Error deciding trade: {str(e)}")
            return None

    def record_trade(self, trade_details: Dict) -> None:
        """
        Record trade details in history.

        Args:
            trade_details: Dictionary with trade details
        """
        try:
            trade_details['timestamp'] = datetime.now()
            self.trade_history.append(trade_details)

            # Keep only last 1000 trades
            if len(self.trade_history) > 1000:
                self.trade_history = self.trade_history[-1000:]

            logger.info(f"Recorded trade: {trade_details}")

        except Exception as e:
            logger.error(f"Error recording trade: {str(e)}")
            raise