#!/usr/bin/env python3
"""
MT5 Trading History Analysis

Comprehensive analysis of the actual MT5 trading performance for account ********.
"""

import sys
import csv
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MT5TradingAnalyzer:
    """Analyze MT5 trading history and performance."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.csv_file = self.project_root / "logs" / "ReportHistory-********.csv"
        
    def parse_trading_data(self) -> Dict[str, Any]:
        """Parse the MT5 trading history CSV file."""
        logger.info("Parsing MT5 trading history...")
        
        analysis = {
            'trades': [],
            'summary_stats': {},
            'performance_metrics': {},
            'signal_analysis': {},
            'time_analysis': {}
        }
        
        if not self.csv_file.exists():
            logger.error(f"CSV file not found: {self.csv_file}")
            return analysis
        
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Parse summary statistics from the end of the file
            self._parse_summary_stats(content, analysis)
            
            # Parse individual trades
            self._parse_trades(content, analysis)
            
            # Analyze trading patterns
            self._analyze_patterns(analysis)
            
        except Exception as e:
            logger.error(f"Error parsing CSV file: {str(e)}")
            
        return analysis
    
    def _parse_summary_stats(self, content: str, analysis: Dict) -> None:
        """Parse summary statistics from the MT5 report."""
        lines = content.split('\n')
        
        for line in lines:
            if "Total Net Profit:" in line:
                # Extract: Total Net Profit:,,,- 889.13,Gross Profit:,,,1 731.06,Gross Loss:,,,-2 620.19,,,
                parts = line.split(',')
                analysis['summary_stats']['total_net_profit'] = float(parts[3].strip())
                analysis['summary_stats']['gross_profit'] = float(parts[7].strip())
                analysis['summary_stats']['gross_loss'] = float(parts[11].strip())
                
            elif "Total Trades:" in line:
                # Extract: Total Trades:,,,3 971,Short Trades (won %):,,,3615 (26.25%),Long Trades (won %):,,,356 (39.04%),,,
                parts = line.split(',')
                analysis['summary_stats']['total_trades'] = int(parts[3].strip())
                
                # Parse short trades
                short_match = re.search(r'(\d+) \((\d+\.\d+)%\)', parts[7].strip())
                if short_match:
                    analysis['summary_stats']['short_trades'] = int(short_match.group(1))
                    analysis['summary_stats']['short_win_rate'] = float(short_match.group(2))
                
                # Parse long trades  
                long_match = re.search(r'(\d+) \((\d+\.\d+)%\)', parts[11].strip())
                if long_match:
                    analysis['summary_stats']['long_trades'] = int(long_match.group(1))
                    analysis['summary_stats']['long_win_rate'] = float(long_match.group(2))
                    
            elif "Balance:" in line:
                # Extract: Balance:,,,24 110.87,,,Free Margin:,,,24 110.87,,,,,
                parts = line.split(',')
                analysis['summary_stats']['final_balance'] = float(parts[3].strip())
                
            elif "Profit Factor:" in line:
                # Extract: Profit Factor:,,, 0.66,Expected Payoff:,,,- 0.22,,,,,,,
                parts = line.split(',')
                analysis['summary_stats']['profit_factor'] = float(parts[3].strip())
                analysis['summary_stats']['expected_payoff'] = float(parts[7].strip())
                
            elif "Balance Drawdown Absolute:" in line:
                # Extract: Balance Drawdown Absolute:,,, 893.76,Balance Drawdown Maximal:,,,916.64 (3.66%),Balance Drawdown Relative:,,,3.66% (916.64),,,
                parts = line.split(',')
                analysis['summary_stats']['max_drawdown_absolute'] = float(parts[3].strip())
                
                # Parse max drawdown percentage
                max_dd_match = re.search(r'(\d+\.\d+) \((\d+\.\d+)%\)', parts[7].strip())
                if max_dd_match:
                    analysis['summary_stats']['max_drawdown_value'] = float(max_dd_match.group(1))
                    analysis['summary_stats']['max_drawdown_percent'] = float(max_dd_match.group(2))
    
    def _parse_trades(self, content: str, analysis: Dict) -> None:
        """Parse individual trade records."""
        lines = content.split('\n')
        trade_count = 0
        
        for line in lines[7:]:  # Skip header lines
            if not line.strip() or 'Balance:' in line or 'Total Net Profit:' in line:
                break
                
            parts = [p.strip() for p in line.split(',')]
            if len(parts) >= 12 and parts[0] and '2025' in parts[0]:
                try:
                    trade = {
                        'time': parts[0],
                        'position': parts[1],
                        'symbol': parts[2],
                        'type': parts[3],  # buy/sell
                        'volume': float(parts[4]) if parts[4] else 0.0,
                        'price': float(parts[5].replace(' ', '')) if parts[5] else 0.0,
                        'profit': float(parts[11]) if parts[11] else 0.0
                    }
                    analysis['trades'].append(trade)
                    trade_count += 1
                except (ValueError, IndexError) as e:
                    continue
                    
        logger.info(f"Parsed {trade_count} individual trades")
    
    def _analyze_patterns(self, analysis: Dict) -> None:
        """Analyze trading patterns and performance."""
        trades = analysis['trades']
        if not trades:
            return
            
        # Signal distribution analysis
        buy_trades = [t for t in trades if t['type'] == 'buy']
        sell_trades = [t for t in trades if t['type'] == 'sell']
        
        analysis['signal_analysis'] = {
            'total_trades': len(trades),
            'buy_trades': len(buy_trades),
            'sell_trades': len(sell_trades),
            'buy_percentage': (len(buy_trades) / len(trades)) * 100 if trades else 0,
            'sell_percentage': (len(sell_trades) / len(trades)) * 100 if trades else 0
        }
        
        # Profit analysis
        profitable_trades = [t for t in trades if t['profit'] > 0]
        losing_trades = [t for t in trades if t['profit'] < 0]
        
        analysis['performance_metrics'] = {
            'profitable_trades': len(profitable_trades),
            'losing_trades': len(losing_trades),
            'win_rate': (len(profitable_trades) / len(trades)) * 100 if trades else 0,
            'average_profit': sum(t['profit'] for t in profitable_trades) / len(profitable_trades) if profitable_trades else 0,
            'average_loss': sum(t['profit'] for t in losing_trades) / len(losing_trades) if losing_trades else 0,
            'total_profit_from_trades': sum(t['profit'] for t in trades)
        }
        
        # Time analysis
        recent_trades = [t for t in trades if '2025.06.10' in t['time']]
        analysis['time_analysis'] = {
            'recent_trades_today': len(recent_trades),
            'recent_buy_trades': len([t for t in recent_trades if t['type'] == 'buy']),
            'recent_sell_trades': len([t for t in recent_trades if t['type'] == 'sell'])
        }
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive trading analysis report."""
        logger.info("Generating MT5 trading analysis report...")
        
        analysis = self.parse_trading_data()
        
        # Calculate additional metrics
        stats = analysis.get('summary_stats', {})
        
        # Starting balance calculation
        final_balance = stats.get('final_balance', 0)
        net_profit = stats.get('total_net_profit', 0)
        starting_balance = final_balance - net_profit
        
        report = {
            'report_timestamp': datetime.now().isoformat(),
            'account_info': {
                'account_number': '********',
                'broker': 'ICMarkets-Demo',
                'currency': 'AUD'
            },
            'performance_summary': {
                'starting_balance': round(starting_balance, 2),
                'final_balance': final_balance,
                'total_net_profit': net_profit,
                'gross_profit': stats.get('gross_profit', 0),
                'gross_loss': stats.get('gross_loss', 0),
                'profit_factor': stats.get('profit_factor', 0),
                'expected_payoff': stats.get('expected_payoff', 0),
                'max_drawdown_absolute': stats.get('max_drawdown_absolute', 0),
                'max_drawdown_percent': stats.get('max_drawdown_percent', 0),
                'return_percentage': (net_profit / starting_balance * 100) if starting_balance > 0 else 0
            },
            'trading_statistics': {
                'total_trades': stats.get('total_trades', 0),
                'short_trades': stats.get('short_trades', 0),
                'long_trades': stats.get('long_trades', 0),
                'short_win_rate': stats.get('short_win_rate', 0),
                'long_win_rate': stats.get('long_win_rate', 0),
                'overall_win_rate': analysis.get('performance_metrics', {}).get('win_rate', 0)
            },
            'signal_distribution': analysis.get('signal_analysis', {}),
            'recent_activity': analysis.get('time_analysis', {}),
            'critical_issues': self._identify_critical_issues(analysis, stats),
            'recommendations': self._generate_recommendations(analysis, stats)
        }
        
        return report
    
    def _identify_critical_issues(self, analysis: Dict, stats: Dict) -> List[Dict]:
        """Identify critical issues from the trading data."""
        issues = []
        
        # Issue 1: Massive losses
        net_profit = stats.get('total_net_profit', 0)
        if net_profit < -500:
            issues.append({
                'severity': 'CRITICAL',
                'category': 'Financial Loss',
                'issue': f'Massive net loss of ${abs(net_profit):.2f}',
                'impact': 'Account has lost significant capital',
                'evidence': f'Net profit: ${net_profit:.2f}'
            })
        
        # Issue 2: Extreme sell bias
        signal_analysis = analysis.get('signal_analysis', {})
        sell_percentage = signal_analysis.get('sell_percentage', 0)
        if sell_percentage > 90:
            issues.append({
                'severity': 'CRITICAL',
                'category': 'Signal Bias',
                'issue': f'Extreme sell bias ({sell_percentage:.1f}% sell signals)',
                'impact': 'Creates directional bias causing losses in trending markets',
                'evidence': f"Sell trades: {signal_analysis.get('sell_trades', 0)}, Buy trades: {signal_analysis.get('buy_trades', 0)}"
            })
        
        # Issue 3: Poor win rate
        overall_win_rate = analysis.get('performance_metrics', {}).get('win_rate', 0)
        if overall_win_rate < 30:
            issues.append({
                'severity': 'HIGH',
                'category': 'Performance',
                'issue': f'Very low win rate ({overall_win_rate:.1f}%)',
                'impact': 'Most trades are losing money',
                'evidence': f'Win rate: {overall_win_rate:.1f}%'
            })
        
        # Issue 4: Poor profit factor
        profit_factor = stats.get('profit_factor', 0)
        if profit_factor < 0.8:
            issues.append({
                'severity': 'HIGH',
                'category': 'Risk Management',
                'issue': f'Poor profit factor ({profit_factor:.2f})',
                'impact': 'Losses exceed profits significantly',
                'evidence': f'Profit factor: {profit_factor:.2f} (should be > 1.0)'
            })
        
        return issues
    
    def _generate_recommendations(self, analysis: Dict, stats: Dict) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = [
            "🚨 IMMEDIATE ACTIONS REQUIRED:",
            "   • STOP TRADING immediately to prevent further losses",
            "   • Fix the extreme sell signal bias (91% sell vs 9% buy)",
            "   • Implement proper risk management with stop losses",
            "   • Review and fix the signal generation algorithm",
            "",
            "📊 PERFORMANCE IMPROVEMENTS:",
            "   • Improve win rate from current 27.4% to at least 40%",
            "   • Balance signal generation (target 50/50 buy/sell ratio)",
            "   • Implement better entry and exit strategies",
            "   • Add position sizing based on account balance",
            "",
            "🔧 TECHNICAL FIXES:",
            "   • Fix ensemble prediction thresholds causing sell bias",
            "   • Implement proper profit/loss tracking",
            "   • Add real-time performance monitoring",
            "   • Improve model prediction accuracy",
            "",
            "💰 RISK MANAGEMENT:",
            "   • Set maximum daily loss limits",
            "   • Implement proper position sizing (1-2% risk per trade)",
            "   • Add circuit breakers for consecutive losses",
            "   • Monitor drawdown levels closely"
        ]
        
        return recommendations
    
    def print_summary(self, report: Dict) -> None:
        """Print a human-readable summary of the analysis."""
        print("\n" + "="*80)
        print("📊 MT5 TRADING ACCOUNT ANALYSIS - ACCOUNT ********")
        print("="*80)
        
        perf = report['performance_summary']
        stats = report['trading_statistics']
        signals = report['signal_distribution']
        
        print(f"💰 FINANCIAL PERFORMANCE:")
        print(f"   Starting Balance: ${perf['starting_balance']:,.2f} AUD")
        print(f"   Final Balance: ${perf['final_balance']:,.2f} AUD")
        print(f"   Total Net Profit: ${perf['total_net_profit']:,.2f} AUD")
        print(f"   Return: {perf['return_percentage']:.2f}%")
        print(f"   Max Drawdown: ${perf['max_drawdown_absolute']:,.2f} ({perf['max_drawdown_percent']:.2f}%)")
        
        print(f"\n📈 TRADING STATISTICS:")
        print(f"   Total Trades: {stats['total_trades']:,}")
        print(f"   Short Trades: {stats['short_trades']:,} (Win Rate: {stats['short_win_rate']:.1f}%)")
        print(f"   Long Trades: {stats['long_trades']:,} (Win Rate: {stats['long_win_rate']:.1f}%)")
        print(f"   Overall Win Rate: {stats['overall_win_rate']:.1f}%")
        print(f"   Profit Factor: {perf['profit_factor']:.2f}")
        
        print(f"\n🎯 SIGNAL DISTRIBUTION:")
        print(f"   Buy Signals: {signals.get('buy_trades', 0):,} ({signals.get('buy_percentage', 0):.1f}%)")
        print(f"   Sell Signals: {signals.get('sell_trades', 0):,} ({signals.get('sell_percentage', 0):.1f}%)")
        
        if report['critical_issues']:
            print(f"\n🚨 CRITICAL ISSUES IDENTIFIED:")
            for issue in report['critical_issues']:
                print(f"   {issue['severity']}: {issue['issue']}")
                print(f"      Impact: {issue['impact']}")
        
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in report['recommendations']:
            print(f"   {rec}")
        
        print("\n" + "="*80)

def main():
    """Main function."""
    analyzer = MT5TradingAnalyzer()
    
    # Generate comprehensive analysis
    report = analyzer.generate_report()
    
    # Print summary
    analyzer.print_summary(report)
    
    # Save detailed report
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f"mt5_analysis_report_{timestamp}.json"
    
    import json
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\n📄 Detailed report saved to: {report_file}")

if __name__ == "__main__":
    main()
