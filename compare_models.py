#!/usr/bin/env python
"""
Model Comparison Script

This script compares the performance of LSTM and ARIMA models across all timeframes.
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import glob

# Import unified configuration
from config import get_metrics_path

def load_metrics(model_type):
    """Load metrics for a specific model type."""
    metrics_dir = get_metrics_path()
    metrics_files = glob.glob(str(metrics_dir / f"{model_type}_BTCUSD.a_*.json"))
    
    if not metrics_files:
        print(f"No metrics files found for {model_type} models")
        return None
    
    metrics_data = {}
    
    for file_path in metrics_files:
        with open(file_path, 'r') as f:
            data = json.load(f)
            
        timeframe = data.get('timeframe')
        if not timeframe and 'symbol' in data:
            # Handle summary files
            for tf, metrics in data.get('metrics', {}).items():
                metrics_data[tf] = metrics
        else:
            # Handle individual files
            metrics_data[timeframe] = data.get('metrics', {})
    
    return metrics_data

def create_comparison_table(lstm_metrics, arima_metrics):
    """Create a comparison table of LSTM and ARIMA models."""
    if not lstm_metrics or not arima_metrics:
        print("Missing metrics data")
        return None
    
    # Define timeframes in order
    timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
    
    # Create DataFrame
    comparison_data = []
    
    for tf in timeframes:
        lstm_data = lstm_metrics.get(tf, {})
        arima_data = arima_metrics.get(tf, {})
        
        if not lstm_data and not arima_data:
            continue
        
        row = {
            'Timeframe': tf,
            'LSTM_MSE': lstm_data.get('mse', np.nan),
            'LSTM_RMSE': lstm_data.get('rmse', np.nan),
            'LSTM_MAE': lstm_data.get('mae', np.nan),
            'LSTM_R2': lstm_data.get('r2', np.nan),
            'ARIMA_MSE': arima_data.get('mse', np.nan),
            'ARIMA_RMSE': arima_data.get('rmse', np.nan),
            'ARIMA_MAE': arima_data.get('mae', np.nan),
            'ARIMA_R2': arima_data.get('r2', np.nan)
        }
        
        comparison_data.append(row)
    
    df = pd.DataFrame(comparison_data)
    
    # Add comparison columns
    if not df.empty:
        df['MSE_Ratio'] = df['LSTM_MSE'] / df['ARIMA_MSE']
        df['RMSE_Ratio'] = df['LSTM_RMSE'] / df['ARIMA_RMSE']
        df['MAE_Ratio'] = df['LSTM_MAE'] / df['ARIMA_MAE']
        df['R2_Diff'] = df['LSTM_R2'] - df['ARIMA_R2']
    
    return df

def plot_comparison(comparison_df, metric='RMSE'):
    """Plot comparison of LSTM and ARIMA models for a specific metric."""
    if comparison_df is None or comparison_df.empty:
        print("No data to plot")
        return
    
    plt.figure(figsize=(12, 6))
    
    # Set up the data
    timeframes = comparison_df['Timeframe']
    lstm_values = comparison_df[f'LSTM_{metric}']
    arima_values = comparison_df[f'ARIMA_{metric}']
    
    # Set up the bar positions
    x = np.arange(len(timeframes))
    width = 0.35
    
    # Create the bars
    plt.bar(x - width/2, lstm_values, width, label='LSTM')
    plt.bar(x + width/2, arima_values, width, label='ARIMA')
    
    # Add labels and title
    plt.xlabel('Timeframe')
    plt.ylabel(metric)
    plt.title(f'Comparison of {metric} between LSTM and ARIMA models')
    plt.xticks(x, timeframes)
    plt.legend()
    
    # Add value labels on top of bars
    for i, v in enumerate(lstm_values):
        plt.text(i - width/2, v + 0.01 * max(lstm_values.max(), arima_values.max()), 
                 f'{v:.2f}', ha='center', va='bottom', fontsize=8)
    
    for i, v in enumerate(arima_values):
        plt.text(i + width/2, v + 0.01 * max(lstm_values.max(), arima_values.max()), 
                 f'{v:.2f}', ha='center', va='bottom', fontsize=8)
    
    # Save the plot
    os.makedirs('plots', exist_ok=True)
    plt.savefig(f'plots/comparison_{metric}.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_all_metrics(comparison_df):
    """Plot comparison of all metrics."""
    metrics = ['MSE', 'RMSE', 'MAE', 'R2']
    
    for metric in metrics:
        plot_comparison(comparison_df, metric)

def save_comparison_table(comparison_df):
    """Save comparison table to CSV and JSON."""
    if comparison_df is None or comparison_df.empty:
        print("No data to save")
        return
    
    metrics_dir = get_metrics_path()
    metrics_dir.mkdir(parents=True, exist_ok=True)

    # Save as CSV
    comparison_df.to_csv(str(metrics_dir / 'model_comparison.csv'), index=False)

    # Save as JSON
    comparison_json = comparison_df.to_dict(orient='records')
    with open(str(metrics_dir / 'model_comparison.json'), 'w') as f:
        json.dump(comparison_json, f, indent=4)

    print(f"Comparison table saved to {metrics_dir / 'model_comparison.csv'} and {metrics_dir / 'model_comparison.json'}")

def main():
    """Main function."""
    # Load metrics
    print("Loading LSTM metrics...")
    lstm_metrics = load_metrics('lstm')
    
    print("Loading ARIMA metrics...")
    arima_metrics = load_metrics('arima')
    
    # Create comparison table
    print("Creating comparison table...")
    comparison_df = create_comparison_table(lstm_metrics, arima_metrics)
    
    if comparison_df is not None and not comparison_df.empty:
        # Print comparison table
        print("\nModel Comparison Table:")
        print(comparison_df.to_string(index=False))
        
        # Save comparison table
        save_comparison_table(comparison_df)
        
        # Plot comparison
        print("\nPlotting comparison...")
        plot_all_metrics(comparison_df)
        print("Plots saved to plots/ directory")
    else:
        print("No comparison data available")

if __name__ == "__main__":
    main()
