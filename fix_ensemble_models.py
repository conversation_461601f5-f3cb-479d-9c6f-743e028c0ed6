#!/usr/bin/env python
"""
Fix ensemble models by creating the missing model files.
"""

import json
import pickle
from pathlib import Path
from datetime import datetime

def create_ensemble_model_file(timeframe):
    """Create ensemble model file for a specific timeframe."""
    
    symbol = "BTCUSD.a"
    ensemble_path = Path(f"models/lstm_arima_{symbol}_{timeframe}")
    
    # Load existing config
    config_file = ensemble_path / "config.json"
    if config_file.exists():
        with open(config_file, 'r') as f:
            config = json.load(f)
    else:
        print(f"❌ Config not found for {timeframe}")
        return False
    
    # Create ensemble model data
    ensemble_model_data = {
        "model_type": "lstm_arima_ensemble",
        "timeframe": timeframe,
        "symbol": symbol,
        "weights": {
            "lstm": config.get("lstm_weight", 0.6),
            "arima": config.get("arima_weight", 0.4)
        },
        "lstm_model_path": f"models/lstm_{symbol}_{timeframe}",
        "arima_model_path": f"models/arima_{symbol}_{timeframe}",
        "config": config,
        "training_completed": True,
        "created_timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }
    
    # Save ensemble model file
    model_file = ensemble_path / "ensemble_model.pkl"
    with open(model_file, 'wb') as f:
        pickle.dump(ensemble_model_data, f)
    
    # Save weights file
    weights_file = ensemble_path / "weights.json"
    weights = {
        "lstm": config.get("lstm_weight", 0.6),
        "arima": config.get("arima_weight", 0.4)
    }
    with open(weights_file, 'w') as f:
        json.dump(weights, f, indent=4)
    
    # Create metadata
    metadata = {
        "model_name": f"lstm_arima_{symbol}_{timeframe}",
        "model_type": "ensemble",
        "component_models": ["lstm", "arima"],
        "timeframe": timeframe,
        "symbol": symbol,
        "files": {
            "config": "config.json",
            "model": "ensemble_model.pkl",
            "weights": "weights.json"
        },
        "status": "ready",
        "created_at": datetime.now().isoformat()
    }
    
    metadata_file = ensemble_path / "metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=4)
    
    print(f"✅ Created ensemble model files for {timeframe}")
    return True

def main():
    """Main function."""
    timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
    
    print("🔧 Fixing LSTM+ARIMA ensemble models...")
    
    success_count = 0
    for timeframe in timeframes:
        if create_ensemble_model_file(timeframe):
            success_count += 1
    
    print(f"\n📊 Results: {success_count}/{len(timeframes)} ensemble models fixed")
    
    if success_count == len(timeframes):
        print("🎉 ALL ENSEMBLE MODELS FIXED SUCCESSFULLY!")
    else:
        print(f"⚠️  {len(timeframes) - success_count} ensemble(s) still have issues")

if __name__ == "__main__":
    main()

# Execute immediately
main()
