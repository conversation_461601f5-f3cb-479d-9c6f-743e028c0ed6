#!/usr/bin/env python
"""
LSTM Model Training Script for a Single Timeframe

This script trains an LSTM model on BTCUSD.a data for a single timeframe
using PyTorch with GPU acceleration.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import torch
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
# Removed train_test_split import - using proper temporal splitting instead
from pathlib import Path
import json
from datetime import datetime
import argparse
import matplotlib.pyplot as plt
from typing import List, Optional, Tuple

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

# Import required modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from models.pytorch_lstm_model import LSTMModel, LSTMTrainer as PyTorchLSTMTrainer
from utils.torch_gpu_config import get_gpu_info, select_device, configure_gpu_memory

# Import unified configuration
from config import get_historical_data_path, get_model_path, get_metrics_path, get_plots_path

def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments.

    Returns:
        Namespace containing the parsed arguments
    """
    parser = argparse.ArgumentParser(description='Train LSTM model for a single timeframe')

    # Required arguments
    parser.add_argument('--timeframe', type=str, required=True,
                        help='Timeframe to train (e.g., M5, H1)')

    # Optional arguments
    parser.add_argument('--feature-columns', type=str, default='open,high,low,close,real_volume',
                        help='Comma-separated list of feature columns')
    parser.add_argument('--target-column', type=str, default='close',
                        help='Target column to predict')
    parser.add_argument('--sequence-length', type=int, default=60,
                        help='Length of input sequences')
    parser.add_argument('--hidden-units', type=int, default=64,
                        help='Number of hidden units in LSTM layers')
    parser.add_argument('--num-layers', type=int, default=2,
                        help='Number of LSTM layers')
    parser.add_argument('--dropout-rate', type=float, default=0.2,
                        help='Dropout rate')
    parser.add_argument('--learning-rate', type=float, default=0.001,
                        help='Learning rate')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=32,
                        help='Batch size for training')
    parser.add_argument('--validation-split', type=float, default=0.1,
                        help='Proportion of training data to use for validation')
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Proportion of data to use for testing')
    parser.add_argument('--random-state', type=int, default=42,
                        help='Random state for reproducibility')
    parser.add_argument('--use-gpu', action='store_true', default=True,
                        help='Use GPU for training if available')
    parser.add_argument('--data-dir', type=str, default=None,
                        help='Directory containing data files (uses config if not specified)')

    return parser.parse_args()

def load_data(timeframe: str, data_dir: str) -> Optional[pd.DataFrame]:
    """
    Load data for a specific timeframe.

    Args:
        timeframe: Timeframe to load (e.g., M5, H1)
        data_dir: Directory containing data files

    Returns:
        DataFrame containing the data, or None if loading fails
    """
    try:
        file_path = Path(data_dir) / f"BTCUSD.a_{timeframe}.parquet"
        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return None

        df = pd.read_parquet(file_path)
        logger.info(f"Loaded {len(df)} rows for {timeframe} from {file_path}")
        return df
    except Exception as e:
        logger.error(f"Error loading data for {timeframe}: {str(e)}")
        return None

def preprocess_data(
    df: pd.DataFrame,
    sequence_length: int,
    target_column: str,
    feature_columns: List[str],
    test_size: float
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, StandardScaler, StandardScaler]:
    """
    Preprocess data for LSTM training.

    Args:
        df: DataFrame containing the data
        sequence_length: Length of input sequences
        target_column: Column name to use as target
        feature_columns: List of column names to use as features
        test_size: Proportion of data to use for testing
        random_state: Random state for reproducibility

    Returns:
        Tuple containing:
            X_train: Training features
            X_test: Testing features
            y_train: Training targets
            y_test: Testing targets
            X_scaler: Feature scaler
            y_scaler: Target scaler
    """
    # Extract features and target
    X = df[feature_columns].values
    y = df[target_column].values.reshape(-1, 1)

    # Scale features and target
    X_scaler = StandardScaler()
    y_scaler = StandardScaler()

    X_scaled = X_scaler.fit_transform(X)
    y_scaled = y_scaler.fit_transform(y)

    # Create sequences
    X_sequences = []
    y_sequences = []

    for i in range(len(X_scaled) - sequence_length):
        X_sequences.append(X_scaled[i:i+sequence_length])
        y_sequences.append(y_scaled[i+sequence_length])

    X_sequences = np.array(X_sequences)
    y_sequences = np.array(y_sequences)

    # Split into train and test sets using proper temporal splitting
    split_idx = int(len(X_sequences) * (1 - test_size))
    X_train = X_sequences[:split_idx]
    X_test = X_sequences[split_idx:]
    y_train = y_sequences[:split_idx]
    y_test = y_sequences[split_idx:]

    return X_train, X_test, y_train, y_test, X_scaler, y_scaler

def main() -> None:
    """
    Main function to train an LSTM model.

    This function parses command-line arguments, loads and preprocesses data,
    trains an LSTM model, evaluates it, and saves the model and metrics.
    """
    args = parse_args()

    # Parse feature columns
    feature_columns = args.feature_columns.split(',')

    # Configure GPU
    gpu_info = get_gpu_info()
    if gpu_info['gpu_available'] and args.use_gpu:
        logger.info(f"GPU is available: {gpu_info['gpu_devices']}")
        configure_gpu_memory()
        device = select_device(use_gpu=True)
        logger.info(f"Using device: {device}")
    else:
        if not gpu_info['gpu_available']:
            logger.warning("No GPU available, using CPU for training")
        elif not args.use_gpu:
            logger.warning("GPU available but use_gpu=False, using CPU for training")
        device = torch.device('cpu')

    # Determine data directory
    if args.data_dir is None:
        data_dir = get_historical_data_path()
        logger.info(f"Using configured data directory: {data_dir}")
    else:
        data_dir = args.data_dir
        logger.info(f"Using specified data directory: {data_dir}")

    # Load data
    df = load_data(args.timeframe, str(data_dir))
    if df is None:
        logger.error(f"Failed to load data for {args.timeframe}")
        sys.exit(1)

    # Preprocess data
    X_train, X_test, y_train, y_test, X_scaler, y_scaler = preprocess_data(
        df, args.sequence_length, args.target_column, feature_columns,
        args.test_size
    )

    # Convert data to PyTorch tensors
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.FloatTensor(y_train)
    X_test_tensor = torch.FloatTensor(X_test)
    y_test_tensor = torch.FloatTensor(y_test)

    # Create datasets and data loaders
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

    # Split training data for validation
    train_size = int((1 - args.validation_split) * len(train_dataset))
    val_size = len(train_dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(
        train_dataset, [train_size, val_size],
        generator=torch.Generator().manual_seed(args.random_state)
    )

    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size)
    test_loader = DataLoader(test_dataset, batch_size=args.batch_size)

    # Get input shape
    _, timesteps, features = X_train.shape

    # Create model
    model = LSTMModel(
        input_dim=features,
        hidden_dim=args.hidden_units,
        num_layers=args.num_layers,
        output_dim=1,
        dropout_rate=args.dropout_rate
    )

    # Create trainer
    trainer = PyTorchLSTMTrainer(
        model=model,
        learning_rate=args.learning_rate,
        device=device
    )

    # Train model
    logger.info(f"Training PyTorch LSTM model for BTCUSD.a {args.timeframe} on {device}")
    _ = trainer.train(  # We don't need to use the history here
        train_loader=train_loader,
        val_loader=val_loader,
        epochs=args.epochs,
        patience=10,
        verbose=True
    )

    # Evaluate model
    test_loss = trainer.evaluate(test_loader)
    logger.info(f"Test loss: {test_loss}")

    # Make predictions
    y_pred = trainer.predict(test_loader)

    # Inverse transform predictions and actual values
    y_pred_inv = y_scaler.inverse_transform(y_pred)
    y_test_inv = y_scaler.inverse_transform(y_test)

    # Calculate metrics
    mse = np.mean((y_pred_inv - y_test_inv) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(y_pred_inv - y_test_inv))

    # Calculate R² (coefficient of determination)
    y_mean = np.mean(y_test_inv)
    ss_total = np.sum((y_test_inv - y_mean) ** 2)
    ss_residual = np.sum((y_test_inv - y_pred_inv) ** 2)
    r2 = 1 - (ss_residual / ss_total)

    logger.info(f"MSE: {mse}, RMSE: {rmse}, MAE: {mae}, R²: {r2}")

    # Save model using standardized path
    model_dir = get_model_path(model_name="lstm", timeframe=args.timeframe)
    model_dir.mkdir(parents=True, exist_ok=True)
    model.save(str(model_dir))
    logger.info(f"Model saved to standardized path: {model_dir}")

    # Save scalers
    scalers = {'X_scaler': X_scaler, 'y_scaler': y_scaler}
    torch.save(scalers, model_dir / "scalers.pt")

    # Save configuration
    config = {
        'model_type': 'pytorch_lstm',
        'symbol': 'BTCUSD.a',
        'timeframe': args.timeframe,
        'sequence_length': args.sequence_length,
        'feature_columns': feature_columns,
        'target_column': args.target_column,
        'hidden_units': args.hidden_units,
        'num_layers': args.num_layers,
        'dropout_rate': args.dropout_rate,
        'learning_rate': args.learning_rate,
        'input_shape': (None, timesteps, features),
        'device': str(device),
        'gpu_available': gpu_info['gpu_available'],
        'gpu_used': gpu_info['gpu_available'] and args.use_gpu,
        'pytorch_version': torch.__version__,
        'metrics': {
            'mse': float(mse),
            'rmse': float(rmse),
            'mae': float(mae),
            'r2': float(r2)
        }
    }

    # Save configuration as JSON
    with open(model_dir / "config.json", "w") as f:
        json.dump(config, f, indent=4)

    logger.info(f"Model saved to {model_dir}")

    # Save metrics to a summary file using standardized path
    metrics_dir = get_metrics_path()
    metrics_dir.mkdir(parents=True, exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    summary_file = metrics_dir / f"lstm_BTCUSD.a_{args.timeframe}_{timestamp}.json"

    with open(str(summary_file), 'w') as f:
        json.dump({
            'model_type': 'lstm',
            'symbol': 'BTCUSD.a',
            'timeframe': args.timeframe,
            'metrics': {
                'mse': float(mse),
                'rmse': float(rmse),
                'mae': float(mae),
                'r2': float(r2)
            }
        }, f, indent=4)

    # Plot predictions vs actual
    plt.figure(figsize=(12, 6))
    plt.plot(y_test_inv[:100], label='Actual')
    plt.plot(y_pred_inv[:100], label='Predicted')
    plt.legend()
    plt.title(f'LSTM Model Predictions vs Actual for {args.timeframe}')

    # Save plot using standardized path
    plots_dir = get_plots_path()
    plots_dir.mkdir(parents=True, exist_ok=True)
    plot_file = plots_dir / f'lstm_predictions_{args.timeframe}.png'
    plt.savefig(str(plot_file))
    plt.close()
    logger.info(f"Plot saved to: {plot_file}")

    logger.info(f"Metrics saved to {summary_file}")
    logger.info("Training completed successfully.")

if __name__ == "__main__":
    main()
