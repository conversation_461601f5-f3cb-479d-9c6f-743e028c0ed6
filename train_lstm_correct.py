#!/usr/bin/env python
"""
Correct LSTM Training Script - Based on Documented Successful Implementation

This script implements the EXACT configuration that achieved R² = 0.9997:
- Only 5 basic features: [open, high, low, close, real_volume]
- Hidden units: 64
- Layers: 2
- Dropout: 0.2
- Learning rate: 0.001
- Batch size: 32
- Sequence length: 60
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import torch
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from pathlib import Path
import json
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/train_lstm_correct.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import required modules
from models.pytorch_lstm_model import LSTMModel, LSTMTrainer
from utils.torch_gpu_config import get_gpu_info, select_device, configure_gpu_memory

# Import unified configuration
from config import get_historical_data_path, get_model_path

def load_data(timeframe, data_dir=None):
    """Load data for a specific timeframe."""
    try:
        if data_dir is None:
            data_dir = get_historical_data_path()
        file_path = Path(data_dir) / f"BTCUSD.a_{timeframe}.parquet"
        df = pd.read_parquet(file_path)
        logger.info(f"Loaded {len(df)} rows for {timeframe} from {file_path}")
        return df
    except Exception as e:
        logger.error(f"Error loading data for {timeframe}: {str(e)}")
        return None

def preprocess_data_correct(df, sequence_length=60, target_column='close', test_size=0.2):
    """
    Correct preprocessing using ONLY the 5 basic features that achieved R² = 0.9997.
    NO feature engineering - just raw OHLCV data.
    """
    logger.info("Using CORRECT LSTM preprocessing - 5 basic features only")
    
    # Use ONLY the 5 basic features that achieved R² = 0.9997
    feature_columns = ['open', 'high', 'low', 'close', 'real_volume']
    
    # Validate columns exist
    missing_cols = [col for col in feature_columns + [target_column] if col not in df.columns]
    if missing_cols:
        raise ValueError(f"Missing columns: {missing_cols}")
    
    # Extract only the required columns - CRITICAL FIX
    df_clean = df[feature_columns].copy()  # Don't include target in features!
    target_data = df[target_column].copy()

    # Remove NaN values
    initial_rows = len(df_clean)
    df_clean = df_clean.dropna()
    target_data = target_data.loc[df_clean.index]  # Align target with features
    final_rows = len(df_clean)

    logger.info(f"Using {len(feature_columns)} basic features: {feature_columns}")
    logger.info(f"Removed {initial_rows - final_rows} rows with NaN values")
    logger.info(f"Final dataset: {final_rows} rows")

    # Extract features and target - CRITICAL FIX
    X = df_clean.values  # Only features, no target
    y = target_data.values.reshape(-1, 1)  # Separate target

    # Verify shapes
    logger.info(f"BEFORE SCALING - X shape: {X.shape}, y shape: {y.shape}")
    if X.shape[0] != y.shape[0]:
        raise ValueError(f"Shape mismatch: X has {X.shape[0]} rows, y has {y.shape[0]} rows")
    
    # Scale features and target separately (CRITICAL for LSTM)
    X_scaler = StandardScaler()
    y_scaler = StandardScaler()
    
    X_scaled = X_scaler.fit_transform(X)
    y_scaled = y_scaler.fit_transform(y)

    logger.info(f"AFTER SCALING - X_scaled shape: {X_scaled.shape}, y_scaled shape: {y_scaled.shape}")

    # Create sequences
    X_sequences = []
    y_sequences = []

    for i in range(len(X_scaled) - sequence_length):
        X_sequences.append(X_scaled[i:i+sequence_length])
        y_sequences.append(y_scaled[i+sequence_length])

    X_sequences = np.array(X_sequences)
    y_sequences = np.array(y_sequences)

    logger.info(f"Created {len(X_sequences)} sequences")
    logger.info(f"FINAL SHAPES - X_sequences: {X_sequences.shape}, y_sequences: {y_sequences.shape}")

    # Verify we have exactly 5 features
    if X_sequences.shape[2] != 5:
        logger.error(f"❌ WRONG FEATURE COUNT: Expected 5, got {X_sequences.shape[2]}")
        raise ValueError(f"Expected 5 features, got {X_sequences.shape[2]}")
    else:
        logger.info(f"✅ CORRECT: Using exactly 5 features as documented")
    
    # Temporal split (CRITICAL - no shuffling for time series)
    split_idx = int(len(X_sequences) * (1 - test_size))
    X_train = X_sequences[:split_idx]
    X_test = X_sequences[split_idx:]
    y_train = y_sequences[:split_idx]
    y_test = y_sequences[split_idx:]
    
    return X_train, X_test, y_train, y_test, X_scaler, y_scaler

def train_correct_lstm(timeframe='M5'):
    """Train LSTM using the EXACT configuration that achieved R² = 0.9997."""
    logger.info("="*60)
    logger.info("TRAINING CORRECT LSTM MODEL - DOCUMENTED CONFIGURATION")
    logger.info("="*60)
    logger.info("Configuration:")
    logger.info("- Features: 5 basic [open, high, low, close, real_volume]")
    logger.info("- Hidden units: 64")
    logger.info("- Layers: 2")
    logger.info("- Dropout: 0.2")
    logger.info("- Learning rate: 0.001")
    logger.info("- Batch size: 32")
    logger.info("- Sequence length: 60")
    
    # Configure GPU
    gpu_info = get_gpu_info()
    if gpu_info['gpu_available']:
        configure_gpu_memory()
        device = select_device(use_gpu=True)
        logger.info(f"Using device: {device}")
    else:
        device = torch.device('cpu')
        logger.info("Using CPU")
    
    # Load data
    df = load_data(timeframe)
    if df is None:
        logger.error(f"Failed to load data for {timeframe}")
        return None
    
    # Preprocess data with CORRECT approach
    X_train, X_test, y_train, y_test, X_scaler, y_scaler = preprocess_data_correct(df)
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.FloatTensor(y_train)
    X_test_tensor = torch.FloatTensor(X_test)
    y_test_tensor = torch.FloatTensor(y_test)
    
    # Create data loaders
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    test_dataset = TensorDataset(X_test_tensor, y_test_tensor)
    
    # Split for validation
    train_size = int(0.9 * len(train_dataset))
    val_size = len(train_dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(
        train_dataset, [train_size, val_size], generator=torch.Generator().manual_seed(42)
    )
    
    # EXACT batch size from documentation
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32)
    test_loader = DataLoader(test_dataset, batch_size=32)
    
    # Create model with EXACT documented architecture
    _, timesteps, features = X_train.shape
    model = LSTMModel(
        input_dim=features,      # Should be 5
        hidden_dim=64,           # EXACT from documentation
        num_layers=2,            # EXACT from documentation
        output_dim=1,
        dropout_rate=0.2         # EXACT from documentation
    )
    
    logger.info(f"Model created: {features} features -> {64} hidden units -> 1 output")
    
    # Create trainer with EXACT parameters
    trainer = LSTMTrainer(
        model=model,
        learning_rate=0.001,     # EXACT from documentation
        device=device
    )
    
    # Train model with EXACT parameters
    logger.info("Training LSTM with documented configuration...")
    history = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        epochs=100,              # EXACT from documentation
        patience=10,             # EXACT from documentation
        verbose=True
    )
    
    # Evaluate
    test_loss = trainer.evaluate(test_loader)
    y_pred = trainer.predict(test_loader)
    
    # Inverse transform predictions
    y_pred_inv = y_scaler.inverse_transform(y_pred)
    y_test_inv = y_scaler.inverse_transform(y_test)
    
    # Calculate metrics
    mse = np.mean((y_pred_inv - y_test_inv) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(y_pred_inv - y_test_inv))
    
    y_mean = np.mean(y_test_inv)
    ss_total = np.sum((y_test_inv - y_mean) ** 2)
    ss_residual = np.sum((y_test_inv - y_pred_inv) ** 2)
    r2 = 1 - (ss_residual / ss_total)
    
    logger.info("="*60)
    logger.info("CORRECT LSTM RESULTS")
    logger.info("="*60)
    logger.info(f"MSE: {mse:.2f}")
    logger.info(f"RMSE: {rmse:.2f}")
    logger.info(f"MAE: {mae:.2f}")
    logger.info(f"R²: {r2:.6f}")
    
    # Performance evaluation against documented target
    target_r2 = 0.9997
    if r2 > 0.999:
        logger.info("🎉 EXCELLENT: R² > 0.999 - Matches documented performance!")
        status = "EXCELLENT"
    elif r2 > 0.99:
        logger.info("✅ VERY GOOD: R² > 0.99 - Close to documented performance")
        status = "VERY_GOOD"
    elif r2 > 0.95:
        logger.info("✅ GOOD: R² > 0.95 - Good performance")
        status = "GOOD"
    elif r2 > 0.80:
        logger.info("⚠️ MODERATE: R² > 0.80 - Moderate performance")
        status = "MODERATE"
    else:
        logger.info("❌ POOR: R² < 0.80 - Still needs improvement")
        status = "POOR"
    
    # Compare with documented performance
    improvement_vs_previous = r2 - 0.244  # Previous best attempt
    logger.info("="*60)
    logger.info("PERFORMANCE COMPARISON")
    logger.info("="*60)
    logger.info(f"Target R² (documented): {target_r2:.4f}")
    logger.info(f"Current R²:             {r2:.6f}")
    logger.info(f"Previous best R²:       0.244000")
    logger.info(f"Improvement vs previous: +{improvement_vs_previous:.3f}")
    logger.info(f"Gap to target:          {target_r2 - r2:.6f}")
    logger.info(f"Status: {status}")
    
    # Save model if performance is good
    if r2 > 0.90:
        model_dir = get_model_path(model_name="lstm", timeframe=timeframe)
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # Save model
        torch.save(model.state_dict(), model_dir / 'model.pt')
        torch.save({'X_scaler': X_scaler, 'y_scaler': y_scaler}, model_dir / 'scalers.pt')
        
        # Save config
        config = {
            'model_type': 'pytorch_lstm_correct',
            'timeframe': timeframe,
            'features': 5,
            'feature_columns': ['open', 'high', 'low', 'close', 'real_volume'],
            'hidden_dim': 64,
            'num_layers': 2,
            'dropout_rate': 0.2,
            'learning_rate': 0.001,
            'batch_size': 32,
            'sequence_length': 60,
            'r2': float(r2),
            'mse': float(mse),
            'rmse': float(rmse),
            'mae': float(mae),
            'timestamp': datetime.now().isoformat()
        }
        
        with open(model_dir / 'config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"✅ Model saved to {model_dir}")
    
    return {
        'r2': r2,
        'mse': mse,
        'rmse': rmse,
        'mae': mae,
        'status': status,
        'model': model,
        'scalers': {'X_scaler': X_scaler, 'y_scaler': y_scaler}
    }

def main():
    """Main function."""
    os.makedirs('logs', exist_ok=True)
    
    result = train_correct_lstm('M5')
    
    if result and result['r2'] > 0.99:
        logger.info("🎉 SUCCESS: LSTM achieved excellent performance with correct configuration!")
        return 0
    elif result and result['r2'] > 0.90:
        logger.info("✅ GOOD: LSTM achieved good performance - significant improvement!")
        return 0
    else:
        logger.info("❌ NEEDS INVESTIGATION: Even correct configuration didn't achieve target")
        return 1

if __name__ == "__main__":
    sys.exit(main())
