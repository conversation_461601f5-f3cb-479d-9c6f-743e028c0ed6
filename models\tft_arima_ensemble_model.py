"""
TFT+ARIMA Ensemble Model Implementation

This module implements a TFT+ARIMA ensemble model that combines
Temporal Fusion Transformer predictions with ARIMA model predictions.
"""

import logging
import json
import pickle
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Optional, Union, Tuple

from models.base_model import BaseModel
from models.pytorch_tft_model import PyTorchTFTModel
from models.ensemble_arima_model import EnsembleARIMAModel

logger = logging.getLogger(__name__)

class TFTARIMAEnsembleModel(BaseModel):
    """
    TFT+ARIMA Ensemble Model that combines TFT and ARIMA predictions.
    """
    
    def __init__(self, model_name: str = "tft_arima_ensemble", 
                 timeframe: str = "M5", terminal_id: str = "5", 
                 config: Optional[Dict[str, Any]] = None):
        """
        Initialize TFT+ARIMA ensemble model.
        
        Args:
            model_name: Name of the model
            timeframe: Trading timeframe
            terminal_id: Terminal ID
            config: Model configuration
        """
        super().__init__()
        
        self.model_name = model_name
        self.timeframe = timeframe
        self.terminal_id = terminal_id
        self.config = config or {}
        
        # Initialize component models
        self.tft_model = None
        self.arima_model = None
        
        # Ensemble weights
        self.tft_weight = self.config.get('tft_weight', 0.6)
        self.arima_weight = self.config.get('arima_weight', 0.4)
        
        # Model paths
        self.model_dir = None
        self.model_path = None
        
        # Metadata
        self.metadata = {}
        self.weights_config = {}
        
        logger.info(f"Initialized TFT+ARIMA ensemble model for {timeframe} timeframe")
    
    def build(self) -> None:
        """Build the ensemble model by loading component models."""
        try:
            # Set model directory
            symbol = self.config.get('symbol', 'BTCUSD.a')
            self.model_dir = Path('models') / f"tft_arima_{symbol}_{self.timeframe}"
            self.model_path = self.model_dir
            
            if not self.model_dir.exists():
                logger.error(f"TFT+ARIMA ensemble directory not found: {self.model_dir}")
                return
            
            # Load weights configuration
            weights_file = self.model_dir / "weights.json"
            if weights_file.exists():
                with open(weights_file, 'r') as f:
                    self.weights_config = json.load(f)
                    self.tft_weight = self.weights_config.get('tft_weight', 0.6)
                    self.arima_weight = self.weights_config.get('arima_weight', 0.4)
                logger.info(f"Loaded ensemble weights: TFT={self.tft_weight}, ARIMA={self.arima_weight}")
            
            # Load metadata
            metadata_file = self.model_dir / "metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    self.metadata = json.load(f)
                logger.info(f"Loaded ensemble metadata")
            
            # Initialize TFT model
            tft_config = self.config.copy()
            tft_config.update(self.metadata.get('tft_config', {}))
            self.tft_model = PyTorchTFTModel(
                model_name="tft",
                timeframe=self.timeframe,
                terminal_id=self.terminal_id,
                config=tft_config
            )
            
            # Set TFT model path
            tft_model_dir = Path('models') / f"tft_{symbol}_{self.timeframe}"
            self.tft_model.model_path = tft_model_dir
            self.tft_model.model_dir = tft_model_dir
            
            # Load TFT model
            if tft_model_dir.exists():
                self.tft_model.load()
                logger.info("✅ TFT component model loaded successfully")
            else:
                logger.error(f"TFT model directory not found: {tft_model_dir}")
            
            # Initialize ARIMA model
            arima_config = self.config.copy()
            arima_config.update(self.metadata.get('arima_config', {}))
            self.arima_model = EnsembleARIMAModel(config=arima_config)
            
            # Set ARIMA model attributes
            self.arima_model.model_name = "arima"
            self.arima_model.terminal_id = self.terminal_id
            self.arima_model.timeframe = self.timeframe
            
            # Build ARIMA model
            self.arima_model.build()
            logger.info("✅ ARIMA component model loaded successfully")
            
            logger.info(f"✅ TFT+ARIMA ensemble model built successfully")
            
        except Exception as e:
            logger.error(f"Error building TFT+ARIMA ensemble model: {str(e)}")
            raise
    
    def predict(self, data: Union[pd.DataFrame, np.ndarray]) -> np.ndarray:
        """
        Make ensemble predictions using both TFT and ARIMA models.
        
        Args:
            data: Input data for prediction
            
        Returns:
            Ensemble predictions
        """
        try:
            predictions = []
            weights = []
            
            # Get TFT prediction
            if self.tft_model is not None:
                try:
                    tft_pred = self.tft_model.predict(data)
                    if tft_pred is not None and not np.isnan(tft_pred).any():
                        predictions.append(tft_pred)
                        weights.append(self.tft_weight)
                        logger.debug(f"TFT prediction: {tft_pred.shape if hasattr(tft_pred, 'shape') else type(tft_pred)}")
                except Exception as e:
                    logger.warning(f"TFT prediction failed: {str(e)}")
            
            # Get ARIMA prediction
            if self.arima_model is not None:
                try:
                    arima_pred = self.arima_model.predict(data)
                    if arima_pred is not None and not np.isnan(arima_pred).any():
                        predictions.append(arima_pred)
                        weights.append(self.arima_weight)
                        logger.debug(f"ARIMA prediction: {arima_pred.shape if hasattr(arima_pred, 'shape') else type(arima_pred)}")
                except Exception as e:
                    logger.warning(f"ARIMA prediction failed: {str(e)}")
            
            # Combine predictions
            if len(predictions) == 0:
                logger.error("No valid predictions from component models")
                return np.array([0.0])
            elif len(predictions) == 1:
                logger.warning("Only one component model provided prediction")
                return predictions[0]
            else:
                # Normalize weights
                total_weight = sum(weights)
                if total_weight > 0:
                    weights = [w / total_weight for w in weights]
                else:
                    weights = [1.0 / len(weights)] * len(weights)
                
                # Weighted average
                ensemble_pred = sum(pred * weight for pred, weight in zip(predictions, weights))
                logger.debug(f"Ensemble prediction: {ensemble_pred.shape if hasattr(ensemble_pred, 'shape') else type(ensemble_pred)}")
                return ensemble_pred
                
        except Exception as e:
            logger.error(f"Error in TFT+ARIMA ensemble prediction: {str(e)}")
            return np.array([0.0])
    
    def save(self, path: Union[str, Path]) -> None:
        """Save ensemble model configuration."""
        try:
            if isinstance(path, str):
                path = Path(path)
            path.mkdir(parents=True, exist_ok=True)
            
            # Save weights
            weights_file = path / "weights.json"
            weights_data = {
                "tft_weight": self.tft_weight,
                "arima_weight": self.arima_weight,
                "ensemble_type": "tft_arima",
                "optimization_method": "grid_search",
                "validation_score": self.metadata.get('validation_score', 0.0),
                "created_at": self.metadata.get('created_at', ''),
                "model_version": "1.0.0"
            }
            
            with open(weights_file, 'w') as f:
                json.dump(weights_data, f, indent=2)
            
            logger.info(f"Saved TFT+ARIMA ensemble weights to {weights_file}")
            
        except Exception as e:
            logger.error(f"Error saving TFT+ARIMA ensemble model: {str(e)}")
            raise
    
    def load(self, path: Optional[Union[str, Path]] = None) -> None:
        """Load ensemble model configuration."""
        try:
            if path is None:
                path = self.model_path or self.model_dir
            
            if isinstance(path, str):
                path = Path(path)
            
            if not path.exists():
                logger.warning(f"TFT+ARIMA ensemble path does not exist: {path}")
                return
            
            # Load weights
            weights_file = path / "weights.json"
            if weights_file.exists():
                with open(weights_file, 'r') as f:
                    weights_data = json.load(f)
                    self.tft_weight = weights_data.get('tft_weight', 0.6)
                    self.arima_weight = weights_data.get('arima_weight', 0.4)
                    self.weights_config = weights_data
                logger.info(f"Loaded TFT+ARIMA ensemble weights from {weights_file}")
            
            # Load metadata
            metadata_file = path / "metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    self.metadata = json.load(f)
                logger.info(f"Loaded TFT+ARIMA ensemble metadata from {metadata_file}")
            
            # Build component models
            self.build()
            
        except Exception as e:
            logger.error(f"Error loading TFT+ARIMA ensemble model: {str(e)}")
            raise
