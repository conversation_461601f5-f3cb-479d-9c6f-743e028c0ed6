"""
Configuration package for the trading bot.
Provides centralized configuration management through the UnifiedConfigManager.
"""
from .unified_config import (
    config_manager,
    get_config,
    get_mt5_config,
    get_strategy_config,
    get_model_config,
    get_all_model_configs,
    get_system_config,
    get_monitoring_config,
    get_data_base_path,
    get_models_base_path,
    # Standardized path getters
    get_data_path,
    get_model_path,
    get_historical_data_path,
    get_metrics_path,
    get_plots_path,
    get_visualizations_path,
    get_logs_path,
    get_monitoring_path,
    get_reports_path,
    # Configuration classes
    TradingConfig,
    MT5Config,
    StrategyConfig,
    ModelConfig,
    SystemConfig,
    MonitoringConfig,
    MT5TerminalConfig
)

# Backwards compatibility removed to fix circular imports
# Use unified_config directly instead:
# from config.unified_config import config_manager as ConfigurationManager

__all__ = [
    'config_manager',
    'get_config',
    'get_mt5_config',
    'get_strategy_config',
    'get_model_config',
    'get_all_model_configs',
    'get_system_config',
    'get_monitoring_config',
    'get_data_base_path',
    'get_models_base_path',
    'get_data_path',
    'get_model_path',
    'get_historical_data_path',
    'get_metrics_path',
    'get_plots_path',
    'get_visualizations_path',
    'get_logs_path',
    'get_monitoring_path',
    'get_reports_path',
    'TradingConfig',
    'MT5Config',
    'StrategyConfig',
    'ModelConfig',
    'SystemConfig',
    'MonitoringConfig',
    'MT5TerminalConfig'
]