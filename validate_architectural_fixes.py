#!/usr/bin/env python
"""
Architectural Fixes Validation Script

This script validates that the critical architectural fixes are working correctly.
"""

import sys
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all critical imports work without circular dependencies."""
    logger.info("🔍 Testing imports...")
    
    try:
        # Test config imports
        from config.unified_config import config_manager, get_model_path
        logger.info("✅ Config imports successful")
        
        # Test model imports
        from models.pytorch_lstm_model import LSTMModel
        from models.ensemble_arima_model import EnsembleARIMAModel
        logger.info("✅ Model imports successful")
        
        # Test ensemble import
        try:
            from models.lstm_arima_ensemble_model import LSTMARIMAEnsembleModel
            logger.info("✅ Ensemble model import successful")
        except ImportError as e:
            logger.warning(f"⚠️  Ensemble model import failed: {e}")
        
        # Test model manager import
        from utils.model_manager import ModelManager
        logger.info("✅ Model manager import successful")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Import test failed: {e}")
        return False

def test_path_resolution():
    """Test that path resolution works correctly."""
    logger.info("🔍 Testing path resolution...")
    
    try:
        from config.unified_config import get_model_path, get_historical_data_path
        
        # Test model path resolution
        lstm_path = get_model_path(model_name="lstm", timeframe="M5")
        arima_path = get_model_path(model_name="arima", timeframe="M5")
        ensemble_path = get_model_path(model_name="lstm_arima", timeframe="M5")
        
        logger.info(f"✅ LSTM path: {lstm_path}")
        logger.info(f"✅ ARIMA path: {arima_path}")
        logger.info(f"✅ Ensemble path: {ensemble_path}")
        
        # Test data path resolution
        data_path = get_historical_data_path()
        logger.info(f"✅ Data path: {data_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Path resolution test failed: {e}")
        return False

def test_ensemble_models():
    """Test that ensemble models are properly populated."""
    logger.info("🔍 Testing ensemble models...")
    
    try:
        timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
        success_count = 0
        
        for timeframe in timeframes:
            ensemble_dir = Path(f"models/lstm_arima_BTCUSD.a_{timeframe}")
            
            if not ensemble_dir.exists():
                logger.error(f"❌ {timeframe}: Ensemble directory not found")
                continue
            
            required_files = ["weights.json", "metadata.json", "ensemble_model.json"]
            missing_files = []
            
            for file_name in required_files:
                file_path = ensemble_dir / file_name
                if not file_path.exists():
                    missing_files.append(file_name)
            
            if missing_files:
                logger.error(f"❌ {timeframe}: Missing files: {missing_files}")
            else:
                logger.info(f"✅ {timeframe}: All ensemble files present")
                success_count += 1
        
        logger.info(f"📊 Ensemble validation: {success_count}/{len(timeframes)} successful")
        return success_count == len(timeframes)
        
    except Exception as e:
        logger.error(f"❌ Ensemble model test failed: {e}")
        return False

def test_model_manager():
    """Test that ModelManager can handle ensemble models."""
    logger.info("🔍 Testing ModelManager...")
    
    try:
        from utils.model_manager import ModelManager
        from config.unified_config import config_manager
        from utils.error_handler import ErrorHandler
        
        # Create model manager instance
        error_handler = ErrorHandler()
        model_manager = ModelManager(
            config_manager=config_manager,
            error_handler=error_handler,
            terminal_id="test",
            timeframe="M5"
        )
        
        # Check if ensemble models are in model classes
        model_classes = model_manager.model_classes
        
        if 'lstm_arima_ensemble' in model_classes:
            ensemble_class = model_classes['lstm_arima_ensemble']
            logger.info(f"✅ LSTM+ARIMA ensemble class: {ensemble_class.__name__}")
        else:
            logger.error("❌ LSTM+ARIMA ensemble not in model classes")
            return False
        
        if 'tft_arima_ensemble' in model_classes:
            tft_ensemble_class = model_classes['tft_arima_ensemble']
            logger.info(f"✅ TFT+ARIMA ensemble class: {tft_ensemble_class.__name__}")
        else:
            logger.warning("⚠️  TFT+ARIMA ensemble not properly implemented")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ ModelManager test failed: {e}")
        return False

def main():
    """Main validation function."""
    logger.info("=" * 60)
    logger.info("ARCHITECTURAL FIXES VALIDATION")
    logger.info("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Path Resolution", test_path_resolution),
        ("Ensemble Models", test_ensemble_models),
        ("Model Manager", test_model_manager)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name}...")
        try:
            if test_func():
                logger.info(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info("VALIDATION SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Tests passed: {passed}/{total}")
    logger.info(f"Success rate: {passed/total*100:.1f}%")
    
    if passed == total:
        logger.info("🎉 ALL ARCHITECTURAL FIXES VALIDATED SUCCESSFULLY!")
        return 0
    else:
        logger.error(f"❌ {total - passed} test(s) failed - Additional fixes needed")
        return 1

if __name__ == "__main__":
    exit(main())
