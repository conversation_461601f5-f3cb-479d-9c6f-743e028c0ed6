#!/usr/bin/env python
"""
Comprehensive System Status Report

This script provides a detailed overview of the entire trading bot system,
including configuration, models, data, and terminal assignments.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Any
import pandas as pd
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SystemStatusReporter:
    """Generates comprehensive system status reports."""
    
    def __init__(self):
        self.report = {
            'timestamp': datetime.now().isoformat(),
            'system_overview': {},
            'configuration': {},
            'models': {},
            'data': {},
            'terminals': {},
            'dependencies': {}
        }
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive system status report."""
        logger.info("Generating comprehensive system status report...")
        
        self._analyze_system_overview()
        self._analyze_configuration()
        self._analyze_models()
        self._analyze_data()
        self._analyze_terminals()
        self._analyze_dependencies()
        
        return self.report
    
    def _analyze_system_overview(self):
        """Analyze overall system status."""
        logger.info("Analyzing system overview...")
        
        overview = {
            'system_name': 'BTCUSD.a Multi-Terminal Trading Bot',
            'version': '2.0.0',
            'architecture': 'Multi-Model Ensemble with Terminal Specialization',
            'terminals_count': 5,
            'models_count': 5,
            'timeframes': ['M5', 'M15', 'M30', 'H1', 'H4'],
            'symbol': 'BTCUSD.a',
            'status': 'OPERATIONAL'
        }
        
        # Check if all critical directories exist
        critical_dirs = ['config', 'models', 'data', 'logs', 'utils', 'trading']
        missing_dirs = [d for d in critical_dirs if not Path(d).exists()]
        
        if missing_dirs:
            overview['status'] = 'DEGRADED'
            overview['missing_directories'] = missing_dirs
        
        self.report['system_overview'] = overview
    
    def _analyze_configuration(self):
        """Analyze configuration status."""
        logger.info("Analyzing configuration...")
        
        config_status = {
            'main_config': 'NOT_FOUND',
            'credentials': 'NOT_FOUND',
            'unified_config': 'NOT_FOUND',
            'terminal_assignments': {},
            'model_weights': {}
        }
        
        # Check main configuration
        try:
            with open('config/config.json', 'r') as f:
                main_config = json.load(f)
            config_status['main_config'] = 'LOADED'
            
            # Extract terminal assignments
            terminal_pairings = main_config.get('terminal_model_pairings', {})
            for terminal_id, config in terminal_pairings.items():
                config_status['terminal_assignments'][terminal_id] = {
                    'model': config.get('primary_model'),
                    'allocation': config.get('allocation'),
                    'description': config.get('description')
                }
            
            # Extract model weights
            ensemble_weights = main_config.get('model_selection', {}).get('ensemble_weights', {})
            config_status['model_weights'] = ensemble_weights
            
        except Exception as e:
            config_status['main_config_error'] = str(e)
        
        # Check credentials
        try:
            from config.credentials import MT5_TERMINALS
            config_status['credentials'] = 'LOADED'
            config_status['terminals_configured'] = len(MT5_TERMINALS)
        except Exception as e:
            config_status['credentials_error'] = str(e)
        
        # Check unified config
        try:
            from config.unified_config import UnifiedConfigManager
            config_manager = UnifiedConfigManager()
            config_status['unified_config'] = 'LOADED'
        except Exception as e:
            config_status['unified_config_error'] = str(e)
        
        self.report['configuration'] = config_status
    
    def _analyze_models(self):
        """Analyze model status."""
        logger.info("Analyzing models...")
        
        models_status = {
            'total_models': 0,
            'by_type': {},
            'by_timeframe': {},
            'missing_models': [],
            'model_details': {}
        }
        
        models_dir = Path('models')
        if not models_dir.exists():
            models_status['error'] = 'Models directory not found'
            self.report['models'] = models_status
            return
        
        # Expected models
        model_types = ['lstm', 'tft', 'arima', 'tft_arima', 'lstm_arima']
        timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
        symbol = 'BTCUSD.a'
        
        for model_type in model_types:
            models_status['by_type'][model_type] = {'found': 0, 'missing': 0}
            
        for timeframe in timeframes:
            models_status['by_timeframe'][timeframe] = {'found': 0, 'missing': 0}
        
        # Scan for existing models
        for model_dir in models_dir.iterdir():
            if model_dir.is_dir():
                models_status['total_models'] += 1
                
                # Parse model directory name
                dir_name = model_dir.name
                if '_BTCUSD.a_' in dir_name:
                    parts = dir_name.split('_')
                    if len(parts) >= 3:
                        model_type = parts[0]
                        timeframe = parts[-1]
                        
                        if model_type in models_status['by_type']:
                            models_status['by_type'][model_type]['found'] += 1
                        
                        if timeframe in models_status['by_timeframe']:
                            models_status['by_timeframe'][timeframe]['found'] += 1
                        
                        # Get model details
                        config_file = model_dir / 'config.json'
                        if config_file.exists():
                            try:
                                with open(config_file, 'r') as f:
                                    model_config = json.load(f)
                                models_status['model_details'][dir_name] = {
                                    'type': model_type,
                                    'timeframe': timeframe,
                                    'metrics': model_config.get('metrics', {}),
                                    'status': 'CONFIGURED'
                                }
                            except Exception as e:
                                models_status['model_details'][dir_name] = {
                                    'type': model_type,
                                    'timeframe': timeframe,
                                    'status': 'CONFIG_ERROR',
                                    'error': str(e)
                                }
                        else:
                            models_status['model_details'][dir_name] = {
                                'type': model_type,
                                'timeframe': timeframe,
                                'status': 'NO_CONFIG'
                            }
        
        # Check for missing models
        for model_type in ['lstm', 'tft', 'arima']:
            for timeframe in timeframes:
                expected_dir = f"{model_type}_{symbol}_{timeframe}"
                if not (models_dir / expected_dir).exists():
                    models_status['missing_models'].append(expected_dir)
                    models_status['by_type'][model_type]['missing'] += 1
                    models_status['by_timeframe'][timeframe]['missing'] += 1
        
        self.report['models'] = models_status
    
    def _analyze_data(self):
        """Analyze data status."""
        logger.info("Analyzing data...")
        
        data_status = {
            'historical_data': {},
            'terminal_data': {},
            'data_quality': {}
        }
        
        # Check historical data
        historical_dir = Path('data/historical/btcusd.a')
        if historical_dir.exists():
            timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
            for timeframe in timeframes:
                parquet_file = historical_dir / f'BTCUSD.a_{timeframe}.parquet'
                if parquet_file.exists():
                    try:
                        df = pd.read_parquet(parquet_file)
                        data_status['historical_data'][timeframe] = {
                            'status': 'AVAILABLE',
                            'rows': len(df),
                            'columns': list(df.columns),
                            'date_range': {
                                'start': str(df['time'].min()) if 'time' in df.columns else 'N/A',
                                'end': str(df['time'].max()) if 'time' in df.columns else 'N/A'
                            },
                            'file_size_mb': round(parquet_file.stat().st_size / (1024*1024), 2)
                        }
                    except Exception as e:
                        data_status['historical_data'][timeframe] = {
                            'status': 'ERROR',
                            'error': str(e)
                        }
                else:
                    data_status['historical_data'][timeframe] = {'status': 'MISSING'}
        
        # Check terminal data
        data_dir = Path('data')
        for terminal_id in ['1', '2', '3', '4', '5']:
            terminal_dir = data_dir / f'terminal_{terminal_id}'
            if terminal_dir.exists():
                data_status['terminal_data'][terminal_id] = {
                    'status': 'AVAILABLE',
                    'timeframes': []
                }
                
                for timeframe in ['M5', 'M15', 'M30', 'H1', 'H4']:
                    tf_dir = terminal_dir / timeframe
                    if tf_dir.exists():
                        data_status['terminal_data'][terminal_id]['timeframes'].append(timeframe)
            else:
                data_status['terminal_data'][terminal_id] = {'status': 'MISSING'}
        
        self.report['data'] = data_status
    
    def _analyze_terminals(self):
        """Analyze terminal assignments and status."""
        logger.info("Analyzing terminals...")
        
        terminals_status = {
            'assignments': {
                '1': {'model': 'arima', 'description': 'Terminal 1: ARIMA Models'},
                '2': {'model': 'lstm', 'description': 'Terminal 2: LSTM Models'},
                '3': {'model': 'tft', 'description': 'Terminal 3: TFT Models'},
                '4': {'model': 'lstm_arima_ensemble', 'description': 'Terminal 4: LSTM + ARIMA Ensemble'},
                '5': {'model': 'tft_arima_ensemble', 'description': 'Terminal 5: TFT + ARIMA Ensemble'}
            },
            'credentials_status': 'UNKNOWN',
            'paths_status': {}
        }
        
        # Check credentials
        try:
            from config.credentials import MT5_TERMINALS
            terminals_status['credentials_status'] = 'LOADED'
            
            for terminal_id, config in MT5_TERMINALS.items():
                path = config.get('path', '')
                terminals_status['paths_status'][terminal_id] = {
                    'path': path,
                    'exists': Path(path).exists() if path else False,
                    'login': config.get('login', ''),
                    'server': config.get('server', ''),
                    'model_type': config.get('model_type', 'unknown')
                }
        except Exception as e:
            terminals_status['credentials_error'] = str(e)
        
        self.report['terminals'] = terminals_status
    
    def _analyze_dependencies(self):
        """Analyze system dependencies."""
        logger.info("Analyzing dependencies...")
        
        dependencies_status = {
            'python_modules': {},
            'system_requirements': {}
        }
        
        # Check Python modules
        required_modules = [
            'torch', 'tensorflow', 'pandas', 'numpy', 'sklearn',
            'MetaTrader5', 'pmdarima', 'pytorch_lightning', 'ta'
        ]
        
        for module in required_modules:
            try:
                __import__(module)
                dependencies_status['python_modules'][module] = 'AVAILABLE'
            except ImportError:
                dependencies_status['python_modules'][module] = 'MISSING'
        
        # Check system requirements
        required_dirs = ['logs', 'metrics', 'plots', 'models', 'data', 'config', 'utils', 'trading']
        for dir_name in required_dirs:
            dependencies_status['system_requirements'][dir_name] = 'EXISTS' if Path(dir_name).exists() else 'MISSING'
        
        self.report['dependencies'] = dependencies_status
    
    def print_report(self):
        """Print formatted system status report."""
        print("\n" + "="*100)
        print("COMPREHENSIVE SYSTEM STATUS REPORT")
        print("="*100)
        print(f"Generated: {self.report['timestamp']}")
        
        # System Overview
        print(f"\n📊 SYSTEM OVERVIEW")
        print("-" * 50)
        overview = self.report['system_overview']
        print(f"System: {overview['system_name']}")
        print(f"Version: {overview['version']}")
        print(f"Architecture: {overview['architecture']}")
        print(f"Status: {overview['status']}")
        print(f"Terminals: {overview['terminals_count']}")
        print(f"Models: {overview['models_count']}")
        print(f"Timeframes: {', '.join(overview['timeframes'])}")
        print(f"Symbol: {overview['symbol']}")
        
        # Terminal Assignments
        print(f"\n🖥️  TERMINAL ASSIGNMENTS")
        print("-" * 50)
        assignments = self.report['terminals']['assignments']
        for terminal_id, config in assignments.items():
            print(f"Terminal {terminal_id}: {config['model']} - {config['description']}")
        
        # Models Status
        print(f"\n🤖 MODELS STATUS")
        print("-" * 50)
        models = self.report['models']
        print(f"Total Models Found: {models['total_models']}")
        print(f"Missing Models: {len(models['missing_models'])}")
        
        print("\nBy Model Type:")
        for model_type, stats in models['by_type'].items():
            print(f"  {model_type}: {stats['found']} found, {stats['missing']} missing")
        
        print("\nBy Timeframe:")
        for timeframe, stats in models['by_timeframe'].items():
            print(f"  {timeframe}: {stats['found']} found, {stats['missing']} missing")
        
        # Data Status
        print(f"\n📈 DATA STATUS")
        print("-" * 50)
        historical = self.report['data']['historical_data']
        print("Historical Data:")
        for timeframe, status in historical.items():
            if status['status'] == 'AVAILABLE':
                print(f"  {timeframe}: ✅ {status['rows']:,} rows, {status['file_size_mb']} MB")
            else:
                print(f"  {timeframe}: ❌ {status['status']}")
        
        # Dependencies
        print(f"\n🔧 DEPENDENCIES")
        print("-" * 50)
        modules = self.report['dependencies']['python_modules']
        available = sum(1 for status in modules.values() if status == 'AVAILABLE')
        total = len(modules)
        print(f"Python Modules: {available}/{total} available")
        
        missing_modules = [name for name, status in modules.items() if status == 'MISSING']
        if missing_modules:
            print(f"Missing: {', '.join(missing_modules)}")
        
        print("\n" + "="*100)
        print("SYSTEM STATUS REPORT COMPLETE")
        print("="*100)

def main():
    """Main function to generate and display system status report."""
    reporter = SystemStatusReporter()
    report = reporter.generate_report()
    reporter.print_report()
    
    # Save report to file
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f"system_status_report_{timestamp}.json"
    
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Detailed report saved to {report_file}")
    return 0

if __name__ == "__main__":
    exit(main())
