#!/usr/bin/env python
"""
Optimized LSTM Training Script

This script implements the documented optimal LSTM configuration that achieved R² = 0.9997.
Based on the analysis, the key is using the right balance of features and model complexity.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import torch
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from pathlib import Path
import json
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/train_lstm_optimized.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import required modules
from models.pytorch_lstm_model import LSTMModel, LSTMTrainer
from utils.torch_gpu_config import get_gpu_info, select_device, configure_gpu_memory

# Import unified configuration
from config import get_historical_data_path

def load_data(timeframe, data_dir=None):
    """Load data for a specific timeframe."""
    try:
        if data_dir is None:
            data_dir = get_historical_data_path()
        file_path = Path(data_dir) / f"BTCUSD.a_{timeframe}.parquet"
        df = pd.read_parquet(file_path)
        logger.info(f"Loaded {len(df)} rows for {timeframe} from {file_path}")
        return df
    except Exception as e:
        logger.error(f"Error loading data for {timeframe}: {str(e)}")
        return None

def add_optimal_lstm_features(df):
    """Add optimal feature engineering specifically for LSTM models."""
    logger.info("Adding optimal LSTM feature engineering...")

    # Ensure we have numeric columns only
    numeric_df = df.select_dtypes(include=[np.number]).copy()

    # Ensure we have the required columns
    required_cols = ['open', 'high', 'low', 'close', 'real_volume']
    missing_cols = [col for col in required_cols if col not in numeric_df.columns]
    if missing_cols:
        logger.error(f"Missing required columns: {missing_cols}")
        return None

    features_df = numeric_df.copy()

    # Price-based features (most important for LSTM)
    features_df['returns'] = features_df['close'].pct_change()
    features_df['log_returns'] = np.log(features_df['close'] / features_df['close'].shift(1))
    features_df['price_change'] = features_df['close'].diff()

    # High-Low features
    features_df['hl_ratio'] = features_df['high'] / features_df['low']
    features_df['oc_ratio'] = features_df['open'] / features_df['close']
    features_df['range'] = features_df['high'] - features_df['low']
    features_df['range_pct'] = features_df['range'] / features_df['close']

    # Volume features
    features_df['volume_change'] = features_df['real_volume'].pct_change()
    features_df['price_volume'] = features_df['close'] * features_df['real_volume']

    # Moving averages (key technical indicators)
    for window in [5, 10, 20]:
        features_df[f'sma_{window}'] = features_df['close'].rolling(window).mean()
        features_df[f'ema_{window}'] = features_df['close'].ewm(span=window).mean()
        features_df[f'price_to_sma_{window}'] = features_df['close'] / features_df[f'sma_{window}'] - 1
        features_df[f'price_to_ema_{window}'] = features_df['close'] / features_df[f'ema_{window}'] - 1

    # Volatility measures
    for window in [5, 10]:
        features_df[f'volatility_{window}'] = features_df['close'].rolling(window).std()
        features_df[f'volatility_ratio_{window}'] = features_df[f'volatility_{window}'] / features_df['close']

    # RSI (single most effective)
    delta = features_df['close'].diff()
    gain = delta.where(delta > 0, 0).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / (loss + 1e-8)
    features_df['rsi'] = 100 - (100 / (1 + rs))

    # MACD
    ema12 = features_df['close'].ewm(span=12).mean()
    ema26 = features_df['close'].ewm(span=26).mean()
    features_df['macd'] = ema12 - ema26
    features_df['macd_signal'] = features_df['macd'].ewm(span=9).mean()
    features_df['macd_histogram'] = features_df['macd'] - features_df['macd_signal']

    # Lag features (important for time series) - reduced to avoid too many NaNs
    for lag in [1, 2, 3]:
        features_df[f'close_lag_{lag}'] = features_df['close'].shift(lag)
        features_df[f'returns_lag_{lag}'] = features_df['returns'].shift(lag)

    # Remove NaN values but keep more data
    initial_rows = len(features_df)
    # Only remove rows where more than 50% of values are NaN
    features_df = features_df.dropna(thresh=len(features_df.columns) * 0.5)

    # Fill remaining NaN values with forward fill then backward fill
    features_df = features_df.ffill().bfill()

    # Handle infinite values
    features_df = features_df.replace([np.inf, -np.inf], np.nan)

    # Final check - remove any remaining NaN rows
    features_df = features_df.dropna()

    # Ensure all values are finite
    for col in features_df.columns:
        if not np.isfinite(features_df[col]).all():
            logger.warning(f"Column {col} contains non-finite values, cleaning...")
            features_df[col] = features_df[col].replace([np.inf, -np.inf], np.nan)
            features_df[col] = features_df[col].fillna(features_df[col].median())

    final_rows = len(features_df)

    logger.info(f"Created {len(features_df.columns)} optimal LSTM features")
    logger.info(f"Removed {initial_rows - final_rows} rows with NaN values")
    logger.info(f"Final dataset: {final_rows} rows")

    return features_df

def preprocess_data_optimized(df, sequence_length=60, target_column='close', test_size=0.2):
    """Optimized preprocessing for LSTM with balanced feature engineering."""
    
    # Add optimal features
    df_features = add_optimal_lstm_features(df)
    
    # Use all features except target
    feature_columns = [col for col in df_features.columns if col != target_column]
    logger.info(f"Using {len(feature_columns)} optimized features")
    
    # Extract features and target
    X = df_features[feature_columns].values
    y = df_features[target_column].values.reshape(-1, 1)
    
    # Scale features and target
    X_scaler = StandardScaler()
    y_scaler = StandardScaler()
    
    X_scaled = X_scaler.fit_transform(X)
    y_scaled = y_scaler.fit_transform(y)
    
    # Create sequences
    X_sequences = []
    y_sequences = []
    
    for i in range(len(X_scaled) - sequence_length):
        X_sequences.append(X_scaled[i:i+sequence_length])
        y_sequences.append(y_scaled[i+sequence_length])
    
    X_sequences = np.array(X_sequences)
    y_sequences = np.array(y_sequences)
    
    logger.info(f"Created {len(X_sequences)} sequences")
    logger.info(f"Feature shape: {X_sequences.shape}, Target shape: {y_sequences.shape}")
    
    # Temporal split
    split_idx = int(len(X_sequences) * (1 - test_size))
    X_train = X_sequences[:split_idx]
    X_test = X_sequences[split_idx:]
    y_train = y_sequences[:split_idx]
    y_test = y_sequences[split_idx:]
    
    return X_train, X_test, y_train, y_test, X_scaler, y_scaler

def train_optimized_lstm(timeframe='M5'):
    """Train optimized LSTM model based on documented best practices."""
    logger.info("="*60)
    logger.info("TRAINING OPTIMIZED LSTM MODEL")
    logger.info("="*60)
    
    # Configure GPU
    gpu_info = get_gpu_info()
    if gpu_info['gpu_available']:
        configure_gpu_memory()
        device = select_device(use_gpu=True)
        logger.info(f"Using device: {device}")
    else:
        device = torch.device('cpu')
        logger.info("Using CPU")
    
    # Load data
    df = load_data(timeframe)
    if df is None:
        logger.error(f"Failed to load data for {timeframe}")
        return None
    
    # Preprocess data
    X_train, X_test, y_train, y_test, X_scaler, y_scaler = preprocess_data_optimized(df)
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.FloatTensor(y_train)
    X_test_tensor = torch.FloatTensor(X_test)
    y_test_tensor = torch.FloatTensor(y_test)
    
    # Create data loaders
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    test_dataset = TensorDataset(X_test_tensor, y_test_tensor)
    
    # Split for validation
    train_size = int(0.9 * len(train_dataset))
    val_size = len(train_dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(
        train_dataset, [train_size, val_size], generator=torch.Generator().manual_seed(42)
    )
    
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32)
    test_loader = DataLoader(test_dataset, batch_size=32)
    
    # Create model with optimal architecture
    _, timesteps, features = X_train.shape
    model = LSTMModel(
        input_dim=features,
        hidden_dim=64,  # Optimal from documentation
        num_layers=2,   # Optimal from documentation
        output_dim=1,
        dropout_rate=0.2  # Optimal from documentation
    )
    
    # Create trainer
    trainer = LSTMTrainer(
        model=model,
        learning_rate=0.001,  # Optimal from documentation
        device=device
    )
    
    # Train model
    logger.info("Training optimized LSTM model...")
    history = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        epochs=100,
        patience=10,
        verbose=True
    )
    
    # Evaluate
    test_loss = trainer.evaluate(test_loader)
    y_pred = trainer.predict(test_loader)
    
    # Inverse transform
    y_pred_inv = y_scaler.inverse_transform(y_pred)
    y_test_inv = y_scaler.inverse_transform(y_test)
    
    # Calculate metrics
    mse = np.mean((y_pred_inv - y_test_inv) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(y_pred_inv - y_test_inv))
    
    y_mean = np.mean(y_test_inv)
    ss_total = np.sum((y_test_inv - y_mean) ** 2)
    ss_residual = np.sum((y_test_inv - y_pred_inv) ** 2)
    r2 = 1 - (ss_residual / ss_total)
    
    logger.info("="*60)
    logger.info("OPTIMIZED LSTM RESULTS")
    logger.info("="*60)
    logger.info(f"MSE: {mse:.2f}")
    logger.info(f"RMSE: {rmse:.2f}")
    logger.info(f"MAE: {mae:.2f}")
    logger.info(f"R²: {r2:.6f}")
    
    # Performance evaluation
    if r2 > 0.95:
        logger.info("🎉 EXCELLENT: R² > 0.95 - Optimal performance achieved!")
        status = "EXCELLENT"
    elif r2 > 0.80:
        logger.info("✅ GOOD: R² > 0.80 - Good performance")
        status = "GOOD"
    elif r2 > 0.50:
        logger.info("⚠️ MODERATE: R² > 0.50 - Moderate performance")
        status = "MODERATE"
    else:
        logger.info("❌ POOR: R² < 0.50 - Needs improvement")
        status = "POOR"
    
    return {
        'r2': r2,
        'mse': mse,
        'rmse': rmse,
        'mae': mae,
        'status': status,
        'model': model,
        'scalers': {'X_scaler': X_scaler, 'y_scaler': y_scaler}
    }

def main():
    """Main function."""
    os.makedirs('logs', exist_ok=True)
    
    result = train_optimized_lstm('M5')
    
    if result and result['r2'] > 0.90:
        logger.info("✅ SUCCESS: Optimized LSTM achieved excellent performance!")
        return 0
    else:
        logger.info("❌ NEEDS IMPROVEMENT: LSTM performance below target")
        return 1

if __name__ == "__main__":
    sys.exit(main())
