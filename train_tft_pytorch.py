#!/usr/bin/env python
"""
TFT Model Training Script using PyTorch

This script trains a simplified Temporal Fusion Transformer (TFT) model on BTCUSD.a data
using PyTorch with GPU acceleration.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler

from pathlib import Path
import json
from datetime import datetime
import argparse
import matplotlib.pyplot as plt
from typing import Dict, List, Optional, Tuple, Union

# Import unified configuration
from config import get_metrics_path, get_plots_path

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TemporalFusionTransformer(nn.Module):
    """
    Simplified Temporal Fusion Transformer model implemented in PyTorch.

    This model combines LSTM layers with multi-head attention mechanisms to create
    a powerful architecture for time series forecasting.

    Args:
        input_dim: Number of input features
        hidden_dim: Size of hidden layers
        num_heads: Number of attention heads
        num_layers: Number of LSTM layers
        dropout: Dropout rate (default: 0.1)
    """
    def __init__(self, input_dim: int, hidden_dim: int, num_heads: int, num_layers: int, dropout: float = 0.1):
        super().__init__()

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.num_layers = num_layers
        self.dropout_rate = dropout

        # Input projection
        self.input_projection = nn.Linear(input_dim, hidden_dim)

        # LSTM layers
        self.lstm = nn.LSTM(
            input_size=hidden_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )

        # Multi-head attention
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )

        # Layer normalization
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)

        # Feed-forward network
        self.ffn = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, hidden_dim)
        )

        # Output layer
        self.output_layer = nn.Linear(hidden_dim, 1)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the network.

        Args:
            x: Input tensor of shape (batch_size, sequence_length, input_dim)

        Returns:
            Output tensor of shape (batch_size, 1)
        """
        # Input projection
        x = self.input_projection(x)

        # LSTM encoding
        lstm_out, _ = self.lstm(x)

        # Layer normalization
        norm_lstm = self.norm1(lstm_out)

        # Self-attention
        attn_out, _ = self.attention(norm_lstm, norm_lstm, norm_lstm)
        attn_out = attn_out + lstm_out  # Residual connection

        # Layer normalization
        norm_attn = self.norm2(attn_out)

        # Feed-forward network
        ffn_out = self.ffn(norm_attn)
        ffn_out = ffn_out + attn_out  # Residual connection

        # Output layer - use only the last time step
        output = self.output_layer(ffn_out[:, -1, :])

        return output

    def save(self, path: str) -> None:
        """
        Save the model to a file.

        Args:
            path: Path to save the model
        """
        model_path = Path(path)
        model_path.mkdir(parents=True, exist_ok=True)

        # Save model state dict
        torch.save(self.state_dict(), model_path / "model.pt")

        # Save model architecture
        model_info = {
            'input_dim': self.input_dim,
            'hidden_dim': self.hidden_dim,
            'num_heads': self.num_heads,
            'num_layers': self.num_layers,
            'dropout_rate': self.dropout_rate
        }
        torch.save(model_info, model_path / "model_info.pt")

        logger.info(f"Model saved to {model_path}")

    @classmethod
    def load(cls, path: str, device: Optional[torch.device] = None) -> 'TemporalFusionTransformer':
        """
        Load a model from a file.

        Args:
            path: Path to load the model from
            device: Device to load the model to

        Returns:
            Loaded model
        """
        model_path = Path(path)

        # Load model architecture
        model_info = torch.load(model_path / "model_info.pt", map_location=device)

        # Create model
        model = cls(
            input_dim=model_info['input_dim'],
            hidden_dim=model_info['hidden_dim'],
            num_heads=model_info['num_heads'],
            num_layers=model_info['num_layers'],
            dropout=model_info['dropout_rate']
        )

        # Load model state dict
        model.load_state_dict(torch.load(model_path / "model.pt", map_location=device))

        # Move model to device
        if device:
            model = model.to(device)

        logger.info(f"Model loaded from {model_path}")
        return model

def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments.

    Returns:
        Namespace containing the parsed arguments
    """
    parser = argparse.ArgumentParser(description='Train TFT model using PyTorch')

    # Required arguments
    parser.add_argument('--timeframe', type=str, required=True,
                        help='Timeframe to train (e.g., M5, H1)')

    # Optional arguments
    parser.add_argument('--feature-columns', type=str, default='open,high,low,close,real_volume',
                        help='Comma-separated list of feature columns')
    parser.add_argument('--target-column', type=str, default='close',
                        help='Target column to predict')
    parser.add_argument('--sequence-length', type=int, default=60,
                        help='Length of input sequences')
    parser.add_argument('--hidden-dim', type=int, default=64,
                        help='Hidden dimension for TFT model')
    parser.add_argument('--num-heads', type=int, default=4,
                        help='Number of attention heads')
    parser.add_argument('--num-layers', type=int, default=2,
                        help='Number of LSTM layers')
    parser.add_argument('--dropout-rate', type=float, default=0.1,
                        help='Dropout rate')
    parser.add_argument('--learning-rate', type=float, default=0.001,
                        help='Learning rate')
    parser.add_argument('--epochs', type=int, default=5,
                        help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=32,
                        help='Batch size for training')
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Proportion of data to use for testing')
    parser.add_argument('--random-state', type=int, default=42,
                        help='Random state for reproducibility')
    parser.add_argument('--use-gpu', action='store_true', default=True,
                        help='Use GPU for training if available')
    parser.add_argument('--data-dir', type=str, default='data/historical/btcusd.a',
                        help='Directory containing data files')
    parser.add_argument('--with-arima', action='store_true', default=False,
                        help='Include ARIMA predictions as features')

    return parser.parse_args()

def load_data(timeframe: str, data_dir: str) -> Optional[pd.DataFrame]:
    """
    Load data for a specific timeframe.

    Args:
        timeframe: Timeframe to load (e.g., M5, H1)
        data_dir: Directory containing data files

    Returns:
        DataFrame containing the data, or None if loading fails
    """
    try:
        file_path = Path(data_dir) / f"BTCUSD.a_{timeframe}.parquet"
        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return None

        df = pd.read_parquet(file_path)
        logger.info(f"Loaded {len(df)} rows for {timeframe} from {file_path}")
        return df
    except Exception as e:
        logger.error(f"Error loading data for {timeframe}: {str(e)}")
        return None

def preprocess_data(
    df: pd.DataFrame,
    feature_columns: List[str],
    target_column: str,
    sequence_length: int,
    test_size: float,
    with_arima: bool = False
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, StandardScaler, StandardScaler]:
    """
    Preprocess data for TFT training.

    Args:
        df: DataFrame containing the data
        feature_columns: List of column names to use as features
        target_column: Column name to use as target
        sequence_length: Length of input sequences
        test_size: Proportion of data to use for testing
        with_arima: Whether to add ARIMA predictions as features

    Returns:
        Tuple containing:
            X_train: Training features
            X_test: Testing features
            y_train: Training targets
            y_test: Testing targets
            X_scaler: Feature scaler
            y_scaler: Target scaler
    """
    # Add ARIMA predictions if requested
    if with_arima:
        # For simplicity, we'll use a simple AR(1) model as a feature
        logger.info("Adding AR(1) predictions as a feature")

        # Create a simple AR(1) prediction (previous value)
        df = df.copy()  # Create a copy to avoid modifying the original
        df['arima_pred'] = df[target_column].shift(1)

        # Fill NaN values with the current value
        df['arima_pred'].fillna(df[target_column], inplace=True)

        # Add ARIMA predictions to feature columns
        feature_columns = feature_columns + ['arima_pred']

    # Extract features and target
    X = df[feature_columns].values
    y = df[target_column].values.reshape(-1, 1)

    # Scale features and target
    X_scaler = StandardScaler()
    y_scaler = StandardScaler()

    X_scaled = X_scaler.fit_transform(X)
    y_scaled = y_scaler.fit_transform(y)

    # Create sequences
    X_sequences = []
    y_sequences = []

    for i in range(len(X_scaled) - sequence_length):
        X_sequences.append(X_scaled[i:i+sequence_length])
        y_sequences.append(y_scaled[i+sequence_length])

    X_sequences = np.array(X_sequences)
    y_sequences = np.array(y_sequences)

    # Split into train and test sets (temporal split - no shuffling for time series)
    split_idx = int(len(X_sequences) * (1 - test_size))
    X_train = X_sequences[:split_idx]
    X_test = X_sequences[split_idx:]
    y_train = y_sequences[:split_idx]
    y_test = y_sequences[split_idx:]

    return X_train, X_test, y_train, y_test, X_scaler, y_scaler

def train_tft_model(args: argparse.Namespace) -> Optional[Dict[str, Union[float, str, int, List[str]]]]:
    """
    Train TFT model for a specific timeframe.

    Args:
        args: Command-line arguments

    Returns:
        Dictionary containing model metrics, or None if training fails
    """
    try:
        # Load data
        df = load_data(args.timeframe, args.data_dir)
        if df is None:
            return None

        # Parse feature columns
        feature_columns = args.feature_columns.split(',')

        # Preprocess data
        X_train, X_test, y_train, y_test, X_scaler, y_scaler = preprocess_data(
            df, feature_columns, args.target_column, args.sequence_length, args.test_size, args.with_arima
        )

        # Convert to PyTorch tensors
        X_train_tensor = torch.FloatTensor(X_train)
        y_train_tensor = torch.FloatTensor(y_train)
        X_test_tensor = torch.FloatTensor(X_test)
        y_test_tensor = torch.FloatTensor(y_test)

        # Create datasets and dataloaders
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

        train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=args.batch_size)

        # Set device
        device = torch.device('cuda' if torch.cuda.is_available() and args.use_gpu else 'cpu')
        logger.info(f"Using device: {device}")

        # Create model
        model = TemporalFusionTransformer(
            input_dim=X_train.shape[2],
            hidden_dim=args.hidden_dim,
            num_heads=args.num_heads,
            num_layers=args.num_layers,
            dropout=args.dropout_rate
        ).to(device)

        # Define loss function and optimizer with weight decay for regularization
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=args.learning_rate, weight_decay=1e-5)

        # Add learning rate scheduler
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=2)

        # Training loop
        logger.info(f"Training TFT model for BTCUSD.a {args.timeframe}")

        model_type = "tft_arima" if args.with_arima else "tft"
        history = {
            'train_loss': [],
            'val_loss': []
        }

        # Early stopping parameters
        best_val_loss = float('inf')
        patience_counter = 0
        patience = 3  # Stop if validation loss doesn't improve for 3 epochs
        best_model_state = None

        for epoch in range(args.epochs):
            model.train()
            train_loss = 0.0

            for inputs, targets in train_loader:
                inputs, targets = inputs.to(device), targets.to(device)

                # Forward pass
                outputs = model(inputs)
                loss = criterion(outputs, targets)

                # Backward pass and optimize
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()

                train_loss += loss.item() * inputs.size(0)

            train_loss /= len(train_loader.dataset)
            history['train_loss'].append(train_loss)

            # Validation
            model.eval()
            val_loss = 0.0

            with torch.no_grad():
                for inputs, targets in test_loader:
                    inputs, targets = inputs.to(device), targets.to(device)
                    outputs = model(inputs)
                    loss = criterion(outputs, targets)
                    val_loss += loss.item() * inputs.size(0)

            val_loss /= len(test_loader.dataset)
            history['val_loss'].append(val_loss)

            # Learning rate scheduling
            scheduler.step(val_loss)

            # Early stopping logic
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                best_model_state = model.state_dict().copy()
                logger.info(f"Epoch {epoch+1}/{args.epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f} (Best)")
            else:
                patience_counter += 1
                logger.info(f"Epoch {epoch+1}/{args.epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f} (Patience: {patience_counter}/{patience})")

                if patience_counter >= patience:
                    logger.info(f"Early stopping triggered after {epoch+1} epochs")
                    break

        # Restore best model state
        if best_model_state is not None:
            model.load_state_dict(best_model_state)
            logger.info("Restored best model state")

        # Evaluate model
        model.eval()
        y_pred = []
        y_true = []

        with torch.no_grad():
            for inputs, targets in test_loader:
                inputs = inputs.to(device)
                outputs = model(inputs)
                y_pred.extend(outputs.cpu().numpy())
                y_true.extend(targets.numpy())

        y_pred = np.array(y_pred)
        y_true = np.array(y_true)

        # Inverse transform predictions and actual values
        y_pred_inv = y_scaler.inverse_transform(y_pred)
        y_true_inv = y_scaler.inverse_transform(y_true)

        # Calculate metrics
        mse = np.mean((y_pred_inv - y_true_inv) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(y_pred_inv - y_true_inv))

        # Calculate R² (coefficient of determination)
        y_mean = np.mean(y_true_inv)
        ss_total = np.sum((y_true_inv - y_mean) ** 2)
        ss_residual = np.sum((y_true_inv - y_pred_inv) ** 2)
        r2 = 1 - (ss_residual / ss_total)

        logger.info(f"MSE: {mse}, RMSE: {rmse}, MAE: {mae}, R2: {r2}")

        # Save model
        model_dir = Path("models") / f"{model_type}_BTCUSD.a_{args.timeframe}"
        model_dir.mkdir(parents=True, exist_ok=True)

        # Save model using the save method
        model.save(str(model_dir))

        # Save scalers
        torch.save({
            'X_scaler': X_scaler,
            'y_scaler': y_scaler
        }, model_dir / "scalers.pt")

        # Save metrics
        metrics = {
            'mse': float(mse),
            'rmse': float(rmse),
            'mae': float(mae),
            'r2': float(r2),
            'model_type': model_type,
            'timeframe': args.timeframe,
            'feature_columns': feature_columns,
            'target_column': args.target_column,
            'sequence_length': args.sequence_length,
            'hidden_dim': args.hidden_dim,
            'num_heads': args.num_heads,
            'num_layers': args.num_layers,
            'dropout_rate': args.dropout_rate,
            'learning_rate': args.learning_rate,
            'epochs': args.epochs,
            'batch_size': args.batch_size,
            'training_samples': len(X_train),
            'validation_samples': len(X_test),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # Save metrics to file using unified configuration
        metrics_dir = get_metrics_path()
        metrics_dir.mkdir(parents=True, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        metrics_file = metrics_dir / f"{model_type}_BTCUSD.a_{args.timeframe}_{timestamp}.json"

        with open(str(metrics_file), 'w') as f:
            json.dump(metrics, f, indent=4)

        logger.info(f"Metrics saved to {metrics_file}")

        # Plot predictions vs actual
        plt.figure(figsize=(12, 6))
        plt.plot(y_true_inv[:100], label='Actual')
        plt.plot(y_pred_inv[:100], label='Predicted')
        plt.legend()
        plt.title(f'{model_type.upper()} Model Predictions vs Actual for {args.timeframe}')

        # Save plot using unified configuration
        plots_dir = get_plots_path()
        plots_dir.mkdir(parents=True, exist_ok=True)
        plt.savefig(str(plots_dir / f'{model_type}_predictions_{args.timeframe}.png'))
        plt.close()

        return metrics

    except Exception as e:
        logger.error(f"Error training TFT model: {str(e)}", exc_info=True)
        return None

def main() -> None:
    """
    Main function to train a TFT model.

    This function parses command-line arguments and trains a TFT model
    for the specified timeframe.
    """
    args = parse_args()
    result = train_tft_model(args)

    if result:
        logger.info("Training completed successfully.")
    else:
        logger.error("Training failed.")
        sys.exit(1)

if __name__ == "__main__":
    main()
