@echo off
setlocal enabledelayedexpansion
echo ============================================================================
echo                    TESTING ENSEMBLE TRAINING SCRIPT
echo ============================================================================
echo.

REM Test Python availability
echo [TEST 1] Checking Python availability...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Python is not available or not in PATH
    echo Please ensure Python is installed and accessible
    pause
    exit /b 1
)
echo [SUCCESS] Python is available
echo.

REM Test data directory
echo [TEST 2] Checking data directory...
if exist "data\historical\btcusd.a" (
    echo [SUCCESS] Data directory exists
) else (
    echo [ERROR] Data directory not found: data\historical\btcusd.a
    echo Please ensure data files are available
    pause
    exit /b 1
)
echo.

REM Test model checking script
echo [TEST 3] Testing model checking logic...
python -c "import os; tf=['M5','M15','M30','H1','H4']; existing=[t for t in tf if os.path.exists(f'models/arima_BTCUSD.a_{t}')]; print(f'[INFO] Found {len(existing)}/5 ARIMA models'); existing_lstm=[t for t in tf if os.path.exists(f'models/lstm_BTCUSD.a_{t}')]; print(f'[INFO] Found {len(existing_lstm)}/5 LSTM models')"
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Model checking script failed
    pause
    exit /b 1
)
echo [SUCCESS] Model checking works
echo.

REM Test individual training scripts exist
echo [TEST 4] Checking training scripts...
if exist "train_all_arima_models.bat" (
    echo [SUCCESS] ARIMA training script exists
) else (
    echo [ERROR] ARIMA training script not found
    pause
    exit /b 1
)

if exist "train_lstm_btcusd.py" (
    echo [SUCCESS] LSTM training script exists
) else (
    echo [ERROR] LSTM training script not found
    pause
    exit /b 1
)

if exist "compare_all_models.py" (
    echo [SUCCESS] Model comparison script exists
) else (
    echo [ERROR] Model comparison script not found
    pause
    exit /b 1
)

if exist "test_lstm_arima_ensemble.py" (
    echo [SUCCESS] Ensemble test script exists
) else (
    echo [ERROR] Ensemble test script not found
    pause
    exit /b 1
)
echo.

echo ============================================================================
echo                    ALL TESTS PASSED - READY TO TRAIN!
echo ============================================================================
echo.
echo You can now safely run: train_all_arima_lstm_ensemble.bat
echo.
pause
