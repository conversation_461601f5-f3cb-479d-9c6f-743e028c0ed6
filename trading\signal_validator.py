"""
Signal Validator - Responsible for validating trading signals against market conditions.
Prevents invalid trading decisions through comprehensive checks.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    """Enum representing different market regimes."""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    VOLATILE = "volatile"
    UNKNOWN = "unknown"

class SignalValidator:
    """
    Validates trading signals against current market conditions.
    
    This class provides comprehensive validation for trading signals
    to ensure they're compatible with current market conditions and
    to prevent erroneous trading decisions.
    """
    
    def __init__(self,
                base_confidence_threshold: float = 0.6,
                volatility_filter: bool = True,
                enable_strict_validation: bool = True):
        """
        Initialize the signal validator.
        
        Args:
            base_confidence_threshold: Base confidence threshold for signal validation
            volatility_filter: Whether to filter signals in high volatility conditions
            enable_strict_validation: Whether to enable strict validation rules
        """
        self.base_confidence_threshold = base_confidence_threshold
        self.volatility_filter = volatility_filter
        self.enable_strict_validation = enable_strict_validation
        
        # Track previous signals for pattern detection
        self.previous_signals = []
        
        logger.info(f"Signal validator initialized with confidence threshold {base_confidence_threshold}")
    
    def validate_signal(self, 
                       signal: Dict[str, Any], 
                       market_data: Dict[str, pd.DataFrame],
                       current_positions: List[Dict[str, Any]] = None) -> Tuple[bool, Optional[str]]:
        """
        Validate a trading signal against current market conditions.
        
        Args:
            signal: Trading signal to validate
            market_data: Dictionary mapping timeframes to market data
            current_positions: List of current trading positions
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        if not signal or signal.get('action') == 'HOLD':
            return True, None  # HOLD signals are always valid
        
        # Check if we have data to validate against
        if not market_data:
            return False, "No market data available for validation"
        
        # Get the main timeframe data for analysis
        main_timeframe = next(iter(market_data))
        for tf in ['M15', 'H1', 'M5']:  # Prefer common timeframes if available
            if tf in market_data:
                main_timeframe = tf
                break
                
        data = market_data[main_timeframe]
        
        # Initialize validation checks
        validations = []
        
        # 1. Check for extreme volatility
        validations.append(self._check_volatility(signal, data))
        
        # 2. Check for price gaps
        validations.append(self._check_price_gaps(signal, data))
        
        # 3. Check for technical indicator alignment
        validations.append(self._check_indicator_alignment(signal, data))
        
        # 4. Check confidence level
        validations.append(self._check_confidence(signal))
        
        # 5. Check for excessive exposure (if positions provided)
        if current_positions:
            validations.append(self._check_exposure(signal, current_positions))
        
        # 6. Check for pattern repetition
        validations.append(self._check_pattern_repetition(signal))
        
        # 7. Check for out-of-hours trading
        validations.append(self._check_trading_hours(signal))
        
        # 8. Check for abnormal price levels
        validations.append(self._check_price_levels(signal, data))
        
        # Compile validation results
        failed_validations = [v for v in validations if v[0] is False]
        
        if failed_validations:
            # Signal failed one or more validation checks
            error_messages = [msg for _, msg in failed_validations]
            error_message = "; ".join(error_messages)
            logger.warning(f"Signal validation failed: {error_message}")
            return False, error_message
        
        # Signal passed all validation checks
        # Add to history for future pattern detection
        self.previous_signals.append({
            'action': signal['action'],
            'confidence': signal.get('confidence', 0),
            'timestamp': datetime.now()
        })
        
        # Keep only the last 20 signals
        if len(self.previous_signals) > 20:
            self.previous_signals = self.previous_signals[-20:]
        
        return True, None
    
    def _check_volatility(self, signal: Dict[str, Any], data: pd.DataFrame) -> Tuple[bool, Optional[str]]:
        """
        Check for extreme volatility conditions.
        
        Args:
            signal: The trading signal
            data: Market data DataFrame
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        if not self.volatility_filter:
            return True, None
            
        if 'volatility' in data.columns:
            recent_volatility = data['volatility'].iloc[-1]
            volatility_mean = data['volatility'].mean()
            volatility_std = data['volatility'].std()
            
            if volatility_std > 0:
                volatility_z_score = (recent_volatility - volatility_mean) / volatility_std
                
                if abs(volatility_z_score) > 3:  # Extremely abnormal volatility
                    return False, f"Extreme market volatility detected (z-score: {volatility_z_score:.2f})"
                    
                if abs(volatility_z_score) > 2 and signal.get('confidence', 1) < self.base_confidence_threshold * 1.2:
                    return False, f"Insufficient confidence ({signal.get('confidence', 0):.2f}) for current volatility conditions"
        
        # Alternative volatility check using price data
        if 'close' in data.columns and len(data) >= 20:
            returns = data['close'].pct_change().dropna()
            recent_returns = returns.iloc[-5:]  # Last 5 periods
            
            if len(recent_returns) > 0:
                recent_volatility = recent_returns.std()
                overall_volatility = returns.std()
                
                if overall_volatility > 0 and recent_volatility > overall_volatility * 2:
                    return False, f"Recent price volatility ({recent_volatility:.4f}) exceeds historical normal range"
        
        return True, None
    
    def _check_price_gaps(self, signal: Dict[str, Any], data: pd.DataFrame) -> Tuple[bool, Optional[str]]:
        """
        Check for abnormal price gaps.
        
        Args:
            signal: The trading signal
            data: Market data DataFrame
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        if len(data) < 3:
            return True, None
            
        if all(col in data.columns for col in ['open', 'close', 'high', 'low']):
            # Check for gaps between candles
            gaps = []
            for i in range(1, min(4, len(data))):
                prev_close = data['close'].iloc[-i-1]
                curr_open = data['open'].iloc[-i]
                
                gap_pct = abs(curr_open - prev_close) / prev_close
                gaps.append(gap_pct)
            
            max_gap = max(gaps) if gaps else 0
            avg_range = (data['high'] - data['low']).pct_change().mean()
            
            if max_gap > avg_range * 3:
                return False, f"Recent price gap detected ({max_gap:.4%}), which exceeds normal range"
            
            # Check for recent price shocks
            recent_range = data['high'].iloc[-3:].max() - data['low'].iloc[-3:].min()
            avg_day_range = (data['high'] - data['low']).mean()
            
            if recent_range > avg_day_range * 3:
                return False, f"Abnormal price movement in recent bars"
                
        return True, None
    
    def _check_indicator_alignment(self, signal: Dict[str, Any], data: pd.DataFrame) -> Tuple[bool, Optional[str]]:
        """
        Check for alignment between signal and technical indicators.
        
        Args:
            signal: The trading signal
            data: Market data DataFrame
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        if not self.enable_strict_validation:
            return True, None
            
        # Check for conflicting indicator signals if available
        indicator_checks = []
        
        # RSI check
        if 'rsi' in data.columns:
            rsi = data['rsi'].iloc[-1]
            rsi_bullish = rsi < 30
            rsi_bearish = rsi > 70
            
            if signal['action'] == 'BUY' and rsi_bearish:
                indicator_checks.append(f"RSI overbought ({rsi:.1f})")
            elif signal['action'] == 'SELL' and rsi_bullish:
                indicator_checks.append(f"RSI oversold ({rsi:.1f})")
        
        # MACD check
        if all(col in data.columns for col in ['macd', 'macd_signal']):
            macd = data['macd'].iloc[-1]
            macd_signal = data['macd_signal'].iloc[-1]
            macd_bullish = macd > macd_signal
            macd_bearish = macd < macd_signal
            
            if signal['action'] == 'BUY' and macd_bearish:
                indicator_checks.append("MACD bearish")
            elif signal['action'] == 'SELL' and macd_bullish:
                indicator_checks.append("MACD bullish")
        
        # Bollinger Bands check
        if all(col in data.columns for col in ['bb_upper', 'bb_lower', 'close']):
            close = data['close'].iloc[-1]
            bb_upper = data['bb_upper'].iloc[-1]
            bb_lower = data['bb_lower'].iloc[-1]
            
            if signal['action'] == 'BUY' and close > bb_upper:
                indicator_checks.append("Price above upper Bollinger Band")
            elif signal['action'] == 'SELL' and close < bb_lower:
                indicator_checks.append("Price below lower Bollinger Band")
        
        # If multiple indicators contradict the signal, reject it
        if len(indicator_checks) >= 2:
            return False, f"Signal contradicts multiple indicators: {', '.join(indicator_checks)}"
            
        return True, None
    
    def _check_confidence(self, signal: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Check if signal confidence meets requirements.
        
        Args:
            signal: The trading signal
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        confidence = signal.get('confidence', 0)
        
        if confidence < self.base_confidence_threshold:
            return False, f"Signal confidence ({confidence:.2f}) below threshold ({self.base_confidence_threshold:.2f})"
            
        return True, None
    
    def _check_exposure(self, 
                       signal: Dict[str, Any], 
                       current_positions: List[Dict[str, Any]]) -> Tuple[bool, Optional[str]]:
        """
        Check if adding this position would create excessive exposure.
        
        Args:
            signal: The trading signal
            current_positions: List of current trading positions
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        # Count positions by direction
        buy_positions = sum(1 for p in current_positions if p.get('type') == 'buy')
        sell_positions = sum(1 for p in current_positions if p.get('type') == 'sell')
        
        # Set maximum positions per direction
        max_positions_per_direction = 3
        
        if signal['action'] == 'BUY' and buy_positions >= max_positions_per_direction:
            return False, f"Maximum buy positions ({max_positions_per_direction}) already reached"
            
        if signal['action'] == 'SELL' and sell_positions >= max_positions_per_direction:
            return False, f"Maximum sell positions ({max_positions_per_direction}) already reached"
            
        return True, None
    
    def _check_pattern_repetition(self, signal: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Check for repetitive signal patterns that might indicate a problem.
        
        Args:
            signal: The trading signal
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        if len(self.previous_signals) < 3:
            return True, None
            
        # Check for too many signals of the same type in a row
        recent_signals = self.previous_signals[-3:]
        same_direction_count = sum(1 for s in recent_signals if s['action'] == signal['action'])
        
        if same_direction_count >= 3:
            return False, f"Too many consecutive {signal['action']} signals"
            
        # Check for ping-pong pattern (alternating BUY/SELL in short period)
        if len(self.previous_signals) >= 4:
            actions = [s['action'] for s in self.previous_signals[-4:]]
            if actions.count('BUY') >= 2 and actions.count('SELL') >= 2:
                alternating = True
                for i in range(len(actions)-1):
                    if actions[i] == actions[i+1]:
                        alternating = False
                        break
                
                if alternating:
                    return False, "Detected ping-pong trading pattern"
            
        return True, None
    
    def _check_trading_hours(self, signal: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Check if trading during current hours is advisable.
        
        Args:
            signal: The trading signal
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        if not self.enable_strict_validation:
            return True, None
            
        current_hour = datetime.now().hour
        
        # Define low liquidity hours (can be customized per market)
        low_liquidity_hours = [0, 1, 2, 3, 22, 23]  # 10 PM - 3 AM
        
        if current_hour in low_liquidity_hours:
            confidence = signal.get('confidence', 0)
            increased_threshold = self.base_confidence_threshold * 1.5
            
            if confidence < increased_threshold:
                return False, f"Insufficient confidence ({confidence:.2f}) for low-liquidity trading hours"
        
        return True, None
    
    def _check_price_levels(self, signal: Dict[str, Any], data: pd.DataFrame) -> Tuple[bool, Optional[str]]:
        """
        Check if current price levels are suitable for the signal.
        
        Args:
            signal: The trading signal
            data: Market data DataFrame
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        if 'close' not in data.columns or len(data) < 20:
            return True, None
            
        close_prices = data['close']
        current_price = close_prices.iloc[-1]
        
        # Check if price is at extreme levels
        high_52w = close_prices.rolling(min(len(close_prices), 52*5)).max().iloc[-1]  # 52-week high
        low_52w = close_prices.rolling(min(len(close_prices), 52*5)).min().iloc[-1]   # 52-week low
        
        if current_price > high_52w * 0.98 and signal['action'] == 'BUY':
            # Buying near all-time high requires higher confidence
            confidence = signal.get('confidence', 0)
            if confidence < self.base_confidence_threshold * 1.3:
                return False, f"Insufficient confidence ({confidence:.2f}) for buying near 52-week high"
                
        if current_price < low_52w * 1.02 and signal['action'] == 'SELL':
            # Selling near all-time low requires higher confidence
            confidence = signal.get('confidence', 0)
            if confidence < self.base_confidence_threshold * 1.3:
                return False, f"Insufficient confidence ({confidence:.2f}) for selling near 52-week low"
        
        return True, None
    
    def get_validation_summary(self, 
                              signal: Dict[str, Any], 
                              market_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Get a comprehensive validation summary for a signal.
        
        Args:
            signal: Trading signal to validate
            market_data: Dictionary mapping timeframes to market data
            
        Returns:
            Dict[str, Any]: Validation summary with risk assessment
        """
        is_valid, error_message = self.validate_signal(signal, market_data)
        
        # Identify main timeframe for analysis
        main_timeframe = next(iter(market_data))
        for tf in ['M15', 'H1', 'M5']:
            if tf in market_data:
                main_timeframe = tf
                break
                
        data = market_data[main_timeframe]
        
        # Calculate risk metrics
        risk_metrics = self._calculate_risk_metrics(signal, data)
        
        return {
            'is_valid': is_valid,
            'error_message': error_message,
            'risk_assessment': {
                'volatility_risk': risk_metrics.get('volatility_risk', 'unknown'),
                'trend_alignment': risk_metrics.get('trend_alignment', 'unknown'),
                'technical_support': risk_metrics.get('technical_support', 'unknown'),
                'overall_risk': risk_metrics.get('overall_risk', 'unknown')
            },
            'recommended_adjustments': self._get_recommended_adjustments(signal, risk_metrics)
        }
    
    def _calculate_risk_metrics(self, 
                               signal: Dict[str, Any], 
                               data: pd.DataFrame) -> Dict[str, Any]:
        """
        Calculate risk metrics for a signal.
        
        Args:
            signal: The trading signal
            data: Market data DataFrame
            
        Returns:
            Dict[str, Any]: Risk metrics
        """
        metrics = {}
        
        # Volatility risk
        if 'volatility' in data.columns:
            recent_volatility = data['volatility'].iloc[-1]
            avg_volatility = data['volatility'].mean()
            
            if recent_volatility < avg_volatility * 0.7:
                metrics['volatility_risk'] = 'low'
            elif recent_volatility < avg_volatility * 1.3:
                metrics['volatility_risk'] = 'medium'
            else:
                metrics['volatility_risk'] = 'high'
        else:
            # Calculate from price data
            if 'close' in data.columns and len(data) > 20:
                recent_std = data['close'].pct_change().iloc[-10:].std()
                overall_std = data['close'].pct_change().std()
                
                if overall_std > 0:
                    vol_ratio = recent_std / overall_std
                    
                    if vol_ratio < 0.7:
                        metrics['volatility_risk'] = 'low'
                    elif vol_ratio < 1.3:
                        metrics['volatility_risk'] = 'medium'
                    else:
                        metrics['volatility_risk'] = 'high'
        
        # Trend alignment
        if 'close' in data.columns and len(data) > 20:
            # Simple trend detection using moving averages
            if 'ma20' not in data.columns:
                data['ma20'] = data['close'].rolling(20).mean()
                
            if 'ma50' not in data.columns:
                data['ma50'] = data['close'].rolling(50).mean()
                
            current_price = data['close'].iloc[-1]
            ma20 = data['ma20'].iloc[-1]
            ma50 = data['ma50'].iloc[-1]
            
            if signal['action'] == 'BUY':
                if current_price > ma20 and ma20 > ma50:
                    metrics['trend_alignment'] = 'strong'
                elif current_price > ma20 or ma20 > ma50:
                    metrics['trend_alignment'] = 'moderate'
                else:
                    metrics['trend_alignment'] = 'weak'
            else:  # SELL
                if current_price < ma20 and ma20 < ma50:
                    metrics['trend_alignment'] = 'strong'
                elif current_price < ma20 or ma20 < ma50:
                    metrics['trend_alignment'] = 'moderate'
                else:
                    metrics['trend_alignment'] = 'weak'
        
        # Technical indicator support
        indicator_support = 0
        indicator_count = 0
        
        # RSI support
        if 'rsi' in data.columns:
            rsi = data['rsi'].iloc[-1]
            indicator_count += 1
            
            if signal['action'] == 'BUY' and rsi < 40:
                indicator_support += 1
            elif signal['action'] == 'SELL' and rsi > 60:
                indicator_support += 1
        
        # MACD support
        if all(col in data.columns for col in ['macd', 'macd_signal']):
            macd = data['macd'].iloc[-1]
            macd_signal = data['macd_signal'].iloc[-1]
            indicator_count += 1
            
            if signal['action'] == 'BUY' and macd > macd_signal:
                indicator_support += 1
            elif signal['action'] == 'SELL' and macd < macd_signal:
                indicator_support += 1
        
        # Bollinger Bands support
        if all(col in data.columns for col in ['bb_upper', 'bb_lower', 'close']):
            close = data['close'].iloc[-1]
            bb_upper = data['bb_upper'].iloc[-1]
            bb_lower = data['bb_lower'].iloc[-1]
            indicator_count += 1
            
            if signal['action'] == 'BUY' and close < (bb_upper + bb_lower) / 2:
                indicator_support += 1
            elif signal['action'] == 'SELL' and close > (bb_upper + bb_lower) / 2:
                indicator_support += 1
        
        if indicator_count > 0:
            support_ratio = indicator_support / indicator_count
            
            if support_ratio > 0.7:
                metrics['technical_support'] = 'strong'
            elif support_ratio > 0.3:
                metrics['technical_support'] = 'moderate'
            else:
                metrics['technical_support'] = 'weak'
        
        # Calculate overall risk
        risk_scores = {
            'volatility_risk': {'low': 1, 'medium': 2, 'high': 3}.get(metrics.get('volatility_risk', 'medium'), 2),
            'trend_alignment': {'strong': 1, 'moderate': 2, 'weak': 3}.get(metrics.get('trend_alignment', 'moderate'), 2),
            'technical_support': {'strong': 1, 'moderate': 2, 'weak': 3}.get(metrics.get('technical_support', 'moderate'), 2)
        }
        
        avg_risk_score = sum(risk_scores.values()) / len(risk_scores)
        
        if avg_risk_score < 1.7:
            metrics['overall_risk'] = 'low'
        elif avg_risk_score < 2.3:
            metrics['overall_risk'] = 'medium'
        else:
            metrics['overall_risk'] = 'high'
        
        return metrics
    
    def _get_recommended_adjustments(self, 
                                   signal: Dict[str, Any], 
                                   risk_metrics: Dict[str, Any]) -> List[str]:
        """
        Get recommended adjustments based on risk assessment.
        
        Args:
            signal: The trading signal
            risk_metrics: Risk metrics dictionary
            
        Returns:
            List[str]: Recommended adjustments
        """
        recommendations = []
        
        # Adjust position size based on risk
        overall_risk = risk_metrics.get('overall_risk', 'medium')
        if overall_risk == 'high':
            recommendations.append("Reduce position size by 50% due to high risk assessment")
        elif overall_risk == 'medium':
            recommendations.append("Consider reducing position size by 25% due to medium risk")
        
        # Adjust stop loss based on volatility
        volatility_risk = risk_metrics.get('volatility_risk', 'medium')
        if volatility_risk == 'high':
            recommendations.append("Widen stop loss to account for higher volatility")
        elif volatility_risk == 'low':
            recommendations.append("Tighter stop loss may be suitable in low volatility conditions")
        
        # Trend alignment recommendations
        trend_alignment = risk_metrics.get('trend_alignment', 'moderate')
        if trend_alignment == 'weak':
            recommendations.append("Consider shorter target due to weak trend alignment")
        elif trend_alignment == 'strong':
            recommendations.append("Consider trailing stop to capture potential trending move")
        
        return recommendations 